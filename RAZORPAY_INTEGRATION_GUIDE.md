# Razorpay Payment Integration Guide

## Overview

This guide provides comprehensive documentation for the Razorpay payment integration implemented in the EMR (Electronic Medical Records) system. The integration supports secure payment processing for medical services, consultations, lab tests, and other healthcare-related transactions.

## Features

- ✅ **Payment Order Creation**: Create secure payment orders with Razorpay
- ✅ **Payment Verification**: Verify payment signatures for security
- ✅ **Webhook Handling**: Process Razorpay webhooks for real-time updates
- ✅ **Payment Tracking**: Store and track payment records in Cosmos DB
- ✅ **Organization Support**: Multi-organization payment management
- ✅ **Statistics & Reporting**: Payment analytics and reporting
- ✅ **Role-based Access**: Permission-based API access control

## Environment Configuration

Add the following environment variables to your `local.settings.json`:

```json
{
  "Values": {
    "RAZORPAY_KEY_ID": "rzp_test_1DP5mmOlF5G5ag",
    "RAZORPAY_KEY_SECRET": "thisissecretkey"
  }
}
```

**Note**: Replace with your actual Razorpay credentials for production use.

## API Endpoints

### 1. Create Payment Order
**Endpoint**: `POST /api/payments/create-order`

**Request Body**:
```json
{
  "amount": 150.75,
  "currency": "INR",
  "patientId": "patient-123",
  "organizationId": "org-456",
  "appointmentId": "appointment-789",
  "description": "Consultation Fee",
  "metadata": {
    "doctorId": "doctor-101",
    "consultationType": "follow-up"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Payment order created successfully",
  "data": {
    "orderId": "order_xyz123",
    "paymentId": "internal-payment-id",
    "amount": 15075,
    "currency": "INR",
    "receipt": "rcpt_abc_123",
    "status": "created",
    "keyId": "rzp_test_1DP5mmOlF5G5ag"
  }
}
```

### 2. Verify Payment
**Endpoint**: `POST /api/payments/verify`

**Request Body**:
```json
{
  "razorpay_order_id": "order_xyz123",
  "razorpay_payment_id": "pay_abc456",
  "razorpay_signature": "signature_hash"
}
```

**Response**:
```json
{
  "success": true,
  "verified": true,
  "message": "Payment verified successfully",
  "paymentId": "pay_abc456"
}
```

### 3. Get Payment Details
**Endpoint**: `GET /api/payments/details?paymentId=xxx`

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "internal-payment-id",
    "razorpayOrderId": "order_xyz123",
    "razorpayPaymentId": "pay_abc456",
    "amount": 150.75,
    "currency": "INR",
    "status": "completed",
    "notes": {
      "patientId": "patient-123",
      "organizationId": "org-456",
      "description": "Consultation Fee"
    },
    "createdAt": "2025-07-07T04:55:16.731Z",
    "verifiedAt": "2025-07-07T04:56:30.123Z"
  }
}
```

### 4. Get Organization Payments
**Endpoint**: `GET /api/payments/organization?organizationId=xxx&pageSize=20&continuationToken=xxx`

### 5. Get Payment Statistics
**Endpoint**: `GET /api/payments/stats?organizationId=xxx`

### 6. Webhook Handler
**Endpoint**: `POST /api/payments/webhook`

## Frontend Integration

### 1. Install Razorpay Checkout

Add to your HTML:
```html
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
```

Or for React:
```bash
npm install react-razorpay
```

### 2. Create Payment Order

```javascript
const createPaymentOrder = async (orderData) => {
  try {
    const response = await fetch('/api/payments/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(orderData)
    });
    
    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Error creating payment order:', error);
    throw error;
  }
};
```

### 3. Initialize Razorpay Checkout

```javascript
const initializePayment = (orderData, patientInfo) => {
  const options = {
    key: orderData.keyId,
    amount: orderData.amount,
    currency: orderData.currency,
    order_id: orderData.orderId,
    name: 'EMR Healthcare System',
    description: 'Payment for medical services',
    image: '/logo.png', // Your logo
    handler: function(response) {
      // Payment successful
      verifyPayment(response);
    },
    prefill: {
      name: patientInfo.name,
      email: patientInfo.email,
      contact: patientInfo.phone
    },
    notes: {
      patientId: orderData.patientId,
      organizationId: orderData.organizationId
    },
    theme: {
      color: '#3399cc'
    },
    modal: {
      ondismiss: function() {
        // Payment cancelled
        console.log('Payment cancelled by user');
      }
    }
  };
  
  const rzp = new Razorpay(options);
  rzp.on('payment.failed', function(response) {
    // Payment failed
    console.error('Payment failed:', response.error);
    alert('Payment failed: ' + response.error.description);
  });
  
  rzp.open();
};
```

### 4. Verify Payment

```javascript
const verifyPayment = async (response) => {
  try {
    const verifyResponse = await fetch('/api/payments/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        razorpay_order_id: response.razorpay_order_id,
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_signature: response.razorpay_signature
      })
    });
    
    const result = await verifyResponse.json();
    if (result.verified) {
      // Payment verified successfully
      showSuccessMessage('Payment completed successfully!');
      redirectToSuccessPage();
    } else {
      // Payment verification failed
      showErrorMessage('Payment verification failed. Please contact support.');
    }
  } catch (error) {
    console.error('Error verifying payment:', error);
    showErrorMessage('Error processing payment verification.');
  }
};
```

## Complete Integration Example

```javascript
// Complete payment flow
const processPayment = async (paymentData, patientInfo) => {
  try {
    // Step 1: Create payment order
    const orderData = await createPaymentOrder(paymentData);
    
    // Step 2: Initialize Razorpay checkout
    initializePayment(orderData, patientInfo);
    
  } catch (error) {
    console.error('Payment process failed:', error);
    showErrorMessage('Failed to initialize payment. Please try again.');
  }
};

// Usage example
const handlePaymentClick = () => {
  const paymentData = {
    amount: 500.00, // ₹500
    currency: 'INR',
    patientId: 'patient-123',
    organizationId: 'org-456',
    appointmentId: 'appointment-789',
    description: 'Consultation Fee - Dr. Smith'
  };
  
  const patientInfo = {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '**********'
  };
  
  processPayment(paymentData, patientInfo);
};
```

## Database Schema

The payment data is stored in Cosmos DB with the following structure:

```json
{
  "id": "unique-payment-id",
  "razorpayOrderId": "order_xyz123",
  "razorpayPaymentId": "pay_abc456",
  "razorpaySignature": "signature_hash",
  "amount": 150.75,
  "currency": "INR",
  "receipt": "rcpt_abc_123",
  "status": "completed",
  "notes": {
    "patientId": "patient-123",
    "organizationId": "org-456",
    "appointmentId": "appointment-789",
    "description": "Consultation Fee"
  },
  "failureReason": null,
  "createdAt": "2025-07-07T04:55:16.731Z",
  "updatedAt": "2025-07-07T04:56:30.123Z",
  "verifiedAt": "2025-07-07T04:56:30.123Z",
  "capturedAt": null,
  "failedAt": null,
  "paidAt": null
}
```

## Permissions

The following permissions are required for payment operations:

- `payment.access` - Access Payment Module
- `payment.create` - Create Payment Orders
- `payment.verify` - Verify Payments
- `payment.view` - View Payment Details
- `payment.organization.view` - View Organization Payments
- `payment.stats.view` - View Payment Statistics
- `payment.webhook.manage` - Manage Payment Webhooks

## Testing

Run the test suite to verify the integration:

```bash
node simple-payment-test.js
```

## Security Considerations

1. **Environment Variables**: Store Razorpay credentials securely
2. **Signature Verification**: Always verify payment signatures
3. **HTTPS**: Use HTTPS for all payment-related communications
4. **Webhook Security**: Verify webhook signatures
5. **Input Validation**: Validate all payment inputs
6. **Error Handling**: Implement proper error handling
7. **Logging**: Log payment activities for audit trails

## Production Deployment

1. Replace test credentials with production Razorpay keys
2. Configure webhook URLs in Razorpay dashboard
3. Set up proper monitoring and alerting
4. Implement backup and recovery procedures
5. Test thoroughly in staging environment

## Support

For issues or questions regarding the payment integration:

1. Check the test results: `node simple-payment-test.js`
2. Review API logs in Application Insights
3. Verify Razorpay dashboard for transaction details
4. Contact development team for technical support

## Changelog

- **v1.0.0**: Initial Razorpay integration implementation
  - Payment order creation
  - Payment verification
  - Webhook handling
  - Database integration
  - API endpoints
  - Permission system
