const HttpMethod = Object.freeze({
  get: 'GET',
  post: 'POST',
  put: 'PUT',
  delete: 'DELETE',
  patch: 'PATCH',
  options: 'OPTIONS',
  head: 'HEAD',
  trace: 'TRACE',
  connect: 'CONNECT',
})

const AuthMessage = Object.freeze({
  MISSING_TOKEN: 'Missing authorization token',
  TOKEN_EXPIRED: 'Token has been expired',
  NO_PERMISSING: 'User does not have permission',
  COMMON_AUTH_FAILED: 'Login fail',
  SUCCESS: 'success',
  SESSION_EXPIRED: 'Session expired',
})
const PackageType = Object.freeze({
  USER: 'user',
  DEPARTMENT: 'department',
})
const RecordStatus = Object.freeze({
  EDITABLE: 'editable',
  FINALIZED: 'finalized',
})
const LabTestStatus = Object.freeze({
  NOT_PAID: 'Not Paid',
  AWAITED: 'Awaited',
  READY: 'Ready',
  UPLOAD: 'Upload',
  UPLOADED: 'Uploaded',
})
module.exports = {
  HttpMethod,
  AuthMessage,
  PackageType,
  RecordStatus,
  LabTestStatus,
}
