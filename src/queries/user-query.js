const getUsersByOrganizationQuery = (
  organizationId,
  search,
  role,
  isActive,
  sortBy,
  sortOrder,
) => {
  const conditions = []

  if (organizationId) {
    conditions.push(`c.organizationId = '${organizationId}'`)
  }

  if (search) {
    conditions.push(
      `(CONTAINS(c.name, '${search}') OR CONTAINS(c.email, '${search}') OR CONTAINS(c.phone, '${search}'))`,
    )
  }

  if (role) {
    conditions.push(`c.userRole = '${role}'`)
  }

  if (isActive !== undefined) {
    conditions.push(`c.isActive = ${isActive}`)
  }

  let query = 'SELECT * FROM c'
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`
  }
  query += ` ORDER BY c.${sortBy} ${sortOrder.toUpperCase()}`

  return query
}

module.exports = {
  getUsersByOrganizationQuery,
}
