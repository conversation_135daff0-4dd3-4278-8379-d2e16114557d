/**
 * Payment Query Builder for Cosmos DB
 * Contains SQL queries for payment-related operations
 */

/**
 * Get payment by Razorpay order ID
 * @param {string} razorpayOrderId - Razorpay order ID
 * @returns {string} SQL query
 */
const getPaymentByOrderIdQuery = (razorpayOrderId) => {
  return `SELECT * FROM c WHERE c.razorpayOrderId = "${razorpayOrderId}"`
}

/**
 * Get payment by internal payment ID
 * @param {string} paymentId - Internal payment ID
 * @returns {string} SQL query
 */
const getPaymentByIdQuery = (paymentId) => {
  return `SELECT * FROM c WHERE c.id = "${paymentId}"`
}

/**
 * Get payments by organization ID
 * @param {string} organizationId - Organization ID
 * @param {string} status - Payment status filter (optional)
 * @param {string} orderBy - Order by field (default: createdAt)
 * @param {string} sortOrder - Sort order (ASC/DESC, default: DESC)
 * @returns {string} SQL query
 */
const getPaymentsByOrganizationQuery = (organizationId, status = null, orderBy = 'createdAt', sortOrder = 'DESC') => {
  let query = `SELECT * FROM c WHERE c.notes.organizationId = "${organizationId}"`
  
  if (status) {
    query += ` AND c.status = "${status}"`
  }
  
  query += ` ORDER BY c.${orderBy} ${sortOrder}`
  
  return query
}

/**
 * Get payments by patient ID
 * @param {string} patientId - Patient ID
 * @param {string} status - Payment status filter (optional)
 * @param {string} orderBy - Order by field (default: createdAt)
 * @param {string} sortOrder - Sort order (ASC/DESC, default: DESC)
 * @returns {string} SQL query
 */
const getPaymentsByPatientQuery = (patientId, status = null, orderBy = 'createdAt', sortOrder = 'DESC') => {
  let query = `SELECT * FROM c WHERE c.notes.patientId = "${patientId}"`
  
  if (status) {
    query += ` AND c.status = "${status}"`
  }
  
  query += ` ORDER BY c.${orderBy} ${sortOrder}`
  
  return query
}

/**
 * Get payments by appointment ID
 * @param {string} appointmentId - Appointment ID
 * @returns {string} SQL query
 */
const getPaymentsByAppointmentQuery = (appointmentId) => {
  return `SELECT * FROM c WHERE c.notes.appointmentId = "${appointmentId}" ORDER BY c.createdAt DESC`
}

/**
 * Get payments by status
 * @param {string} status - Payment status
 * @param {string} organizationId - Organization ID (optional)
 * @param {string} orderBy - Order by field (default: createdAt)
 * @param {string} sortOrder - Sort order (ASC/DESC, default: DESC)
 * @returns {string} SQL query
 */
const getPaymentsByStatusQuery = (status, organizationId = null, orderBy = 'createdAt', sortOrder = 'DESC') => {
  let query = `SELECT * FROM c WHERE c.status = "${status}"`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  query += ` ORDER BY c.${orderBy} ${sortOrder}`
  
  return query
}

/**
 * Get payments within date range
 * @param {string} startDate - Start date (ISO string)
 * @param {string} endDate - End date (ISO string)
 * @param {string} organizationId - Organization ID (optional)
 * @param {string} status - Payment status filter (optional)
 * @returns {string} SQL query
 */
const getPaymentsByDateRangeQuery = (startDate, endDate, organizationId = null, status = null) => {
  let query = `SELECT * FROM c WHERE c.createdAt >= "${startDate}" AND c.createdAt <= "${endDate}"`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  if (status) {
    query += ` AND c.status = "${status}"`
  }
  
  query += ` ORDER BY c.createdAt DESC`
  
  return query
}

/**
 * Get payment statistics by organization
 * @param {string} organizationId - Organization ID
 * @param {string} startDate - Start date (ISO string, optional)
 * @param {string} endDate - End date (ISO string, optional)
 * @returns {string} SQL query
 */
const getPaymentStatsQuery = (organizationId, startDate = null, endDate = null) => {
  let query = `SELECT 
    COUNT(1) as totalPayments,
    SUM(c.amount) as totalAmount,
    AVG(c.amount) as averageAmount
    FROM c WHERE c.notes.organizationId = "${organizationId}"`
  
  if (startDate && endDate) {
    query += ` AND c.createdAt >= "${startDate}" AND c.createdAt <= "${endDate}"`
  }
  
  return query
}

/**
 * Get payment statistics by status for organization
 * @param {string} organizationId - Organization ID
 * @param {string} startDate - Start date (ISO string, optional)
 * @param {string} endDate - End date (ISO string, optional)
 * @returns {string} SQL query
 */
const getPaymentStatsByStatusQuery = (organizationId, startDate = null, endDate = null) => {
  let query = `SELECT 
    c.status,
    COUNT(1) as count,
    SUM(c.amount) as totalAmount
    FROM c WHERE c.notes.organizationId = "${organizationId}"`
  
  if (startDate && endDate) {
    query += ` AND c.createdAt >= "${startDate}" AND c.createdAt <= "${endDate}"`
  }
  
  query += ` GROUP BY c.status`
  
  return query
}

/**
 * Get recent payments for organization
 * @param {string} organizationId - Organization ID
 * @param {number} limit - Number of recent payments to fetch (default: 10)
 * @returns {string} SQL query
 */
const getRecentPaymentsQuery = (organizationId, limit = 10) => {
  return `SELECT TOP ${limit} * FROM c WHERE c.notes.organizationId = "${organizationId}" ORDER BY c.createdAt DESC`
}

/**
 * Get failed payments for retry
 * @param {string} organizationId - Organization ID (optional)
 * @param {number} hoursAgo - Hours ago to look for failed payments (default: 24)
 * @returns {string} SQL query
 */
const getFailedPaymentsQuery = (organizationId = null, hoursAgo = 24) => {
  const cutoffDate = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString()
  let query = `SELECT * FROM c WHERE c.status = "failed" AND c.createdAt >= "${cutoffDate}"`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  query += ` ORDER BY c.createdAt DESC`
  
  return query
}

/**
 * Get pending payments (created but not completed)
 * @param {string} organizationId - Organization ID (optional)
 * @param {number} hoursAgo - Hours ago to look for pending payments (default: 2)
 * @returns {string} SQL query
 */
const getPendingPaymentsQuery = (organizationId = null, hoursAgo = 2) => {
  const cutoffDate = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString()
  let query = `SELECT * FROM c WHERE c.status = "created" AND c.createdAt <= "${cutoffDate}"`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  query += ` ORDER BY c.createdAt ASC`
  
  return query
}

/**
 * Search payments by receipt or description
 * @param {string} searchTerm - Search term
 * @param {string} organizationId - Organization ID (optional)
 * @returns {string} SQL query
 */
const searchPaymentsQuery = (searchTerm, organizationId = null) => {
  let query = `SELECT * FROM c WHERE (CONTAINS(c.receipt, "${searchTerm}") OR CONTAINS(c.notes.description, "${searchTerm}"))`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  query += ` ORDER BY c.createdAt DESC`
  
  return query
}

/**
 * Get payments by amount range
 * @param {number} minAmount - Minimum amount
 * @param {number} maxAmount - Maximum amount
 * @param {string} organizationId - Organization ID (optional)
 * @returns {string} SQL query
 */
const getPaymentsByAmountRangeQuery = (minAmount, maxAmount, organizationId = null) => {
  let query = `SELECT * FROM c WHERE c.amount >= ${minAmount} AND c.amount <= ${maxAmount}`
  
  if (organizationId) {
    query += ` AND c.notes.organizationId = "${organizationId}"`
  }
  
  query += ` ORDER BY c.amount DESC`
  
  return query
}

/**
 * Get payment summary for dashboard
 * @param {string} organizationId - Organization ID
 * @param {number} days - Number of days to look back (default: 30)
 * @returns {string} SQL query
 */
const getPaymentDashboardSummaryQuery = (organizationId, days = 30) => {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
  
  return `SELECT 
    COUNT(1) as totalPayments,
    SUM(c.amount) as totalAmount,
    SUM(CASE WHEN c.status IN ("completed", "captured", "paid") THEN 1 ELSE 0 END) as successfulPayments,
    SUM(CASE WHEN c.status = "failed" THEN 1 ELSE 0 END) as failedPayments,
    SUM(CASE WHEN c.status = "created" THEN 1 ELSE 0 END) as pendingPayments,
    AVG(c.amount) as averageAmount
    FROM c WHERE c.notes.organizationId = "${organizationId}" AND c.createdAt >= "${startDate}"`
}

module.exports = {
  getPaymentByOrderIdQuery,
  getPaymentByIdQuery,
  getPaymentsByOrganizationQuery,
  getPaymentsByPatientQuery,
  getPaymentsByAppointmentQuery,
  getPaymentsByStatusQuery,
  getPaymentsByDateRangeQuery,
  getPaymentStatsQuery,
  getPaymentStatsByStatusQuery,
  getRecentPaymentsQuery,
  getFailedPaymentsQuery,
  getPendingPaymentsQuery,
  searchPaymentsQuery,
  getPaymentsByAmountRangeQuery,
  getPaymentDashboardSummaryQuery,
}
