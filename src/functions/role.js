const { app } = require('@azure/functions')
const roleHandler = require('../handlers/admin/role-handler')
const { jsonResponse } = require('../common/helper')

app.http('list-roles', {
  methods: ['GET'],
  route: 'list-roles',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return roleHandler.listRoles(req)
    } catch (err) {
      return jsonResponse('Error fetching roles', 500)
    }
  },
})

app.http('role', {
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    switch (req.method) {
      case 'GET':
        try {
          const roleId = req.query.get('roleId')
          if (!roleId) {
            return jsonResponse('Role ID is required', 400)
          }
          return roleHandler.getRole(req)
        } catch (err) {
          return jsonResponse('Error fetching role details', 500)
        }

      case 'POST':
        try {
          return roleHandler.createRole(req)
        } catch (err) {
          return jsonResponse('Error creating role', 500)
        }

      case 'PATCH':
        try {
          return roleHandler.editRole(req)
        } catch (err) {
          return jsonResponse('Error editing role', 500)
        }

      case 'DELETE':
        try {
          return roleHandler.deleteRole(req)
        } catch (err) {
          return jsonResponse('Error deleting role', 500)
        }

      default:
        return jsonResponse('Unsupported HTTP method', 405)
    }
  },
})
