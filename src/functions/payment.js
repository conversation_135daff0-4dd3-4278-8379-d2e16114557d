const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const paymentHandler = require('../handlers/payment-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

// Create payment order
app.http('create-payment-order', {
  methods: ['POST'],
  route: 'payments/create-order',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.createOrder(req)
    } catch (err) {
      context.log.error('Error creating payment order:', err)
      return jsonResponse(
        'Error creating payment order',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Verify payment
app.http('verify-payment', {
  methods: ['POST'],
  route: 'payments/verify',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.verifyPayment(req)
    } catch (err) {
      context.log.error('Error verifying payment:', err)
      return jsonResponse(
        'Error verifying payment',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Handle Razorpay webhooks
app.http('payment-webhook', {
  methods: ['POST'],
  route: 'payments/webhook',
  authLevel: 'anonymous', // Webhooks come from Razorpay, not authenticated users
  handler: async (req, context) => {
    context.log(`Http function processed webhook for url "${req.url}"`)
    
    try {
      return await paymentHandler.handleWebhook(req)
    } catch (err) {
      context.log.error('Error processing payment webhook:', err)
      return jsonResponse(
        'Error processing webhook',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Get payment by order ID
app.http('get-payment-by-order', {
  methods: ['GET'],
  route: 'payments/order',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.getPaymentByOrderId(req)
    } catch (err) {
      context.log.error('Error fetching payment by order ID:', err)
      return jsonResponse(
        'Error fetching payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Get payment by payment ID
app.http('get-payment-by-id', {
  methods: ['GET'],
  route: 'payments/details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.getPaymentById(req)
    } catch (err) {
      context.log.error('Error fetching payment by ID:', err)
      return jsonResponse(
        'Error fetching payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Get organization payments
app.http('get-organization-payments', {
  methods: ['GET'],
  route: 'payments/organization',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.getOrganizationPayments(req)
    } catch (err) {
      context.log.error('Error fetching organization payments:', err)
      return jsonResponse(
        'Error fetching organization payments',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Get payment statistics
app.http('get-payment-stats', {
  methods: ['GET'],
  route: 'payments/stats',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      return await paymentHandler.getPaymentStats(req)
    } catch (err) {
      context.log.error('Error fetching payment statistics:', err)
      return jsonResponse(
        'Error fetching payment statistics',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Generic payment endpoint for multiple operations
app.http('payments', {
  methods: ['GET', 'POST'],
  route: 'payments',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    try {
      switch (req.method) {
        case HttpMethod.post:
          // Check if it's a create order request or verify payment request
          const body = await req.json()
          if (body.razorpay_payment_id && body.razorpay_signature) {
            // This is a verify payment request
            return await paymentHandler.verifyPayment(req)
          } else {
            // This is a create order request
            return await paymentHandler.createOrder(req)
          }
          
        case HttpMethod.get:
          const orderId = req.query.get('orderId')
          const paymentId = req.query.get('paymentId')
          const organizationId = req.query.get('organizationId')
          
          if (orderId) {
            return await paymentHandler.getPaymentByOrderId(req)
          } else if (paymentId) {
            return await paymentHandler.getPaymentById(req)
          } else if (organizationId) {
            return await paymentHandler.getOrganizationPayments(req)
          } else {
            return jsonResponse(
              'Missing required query parameter: orderId, paymentId, or organizationId',
              HttpStatusCode.BadRequest,
            )
          }
          
        default:
          return jsonResponse(
            `Unsupported HTTP method: ${req.method}`,
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (err) {
      context.log.error('Error in payments endpoint:', err)
      return jsonResponse(
        'Error processing payment request',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
