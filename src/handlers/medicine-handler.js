const medicineService = require('../services/medicine-service')
const excelToJson = require('convert-excel-to-json')
const fs = require('fs')
const path = require('path')
const MedicineModel = require('../models/medicine-model')
const { getFilePath } = require('../utils/file-utils')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

class MedicineHandler {
  async getAllMedicines() {
    const data = await medicineService.getAllMedicines()
    return this.transformMedicinesData(data)
  }

  transformMedicinesData(data) {
    return data.map((medicine) => {
      const saltComposition = medicine.saltComposition?.split(' + ') || []

      const strength = saltComposition
        .map((comp) => {
          const percentMatch = comp.match(/(\d+(\.\d+)?)%\s?w\/v/)
          if (percentMatch) {
            return percentMatch[0]
          }

          const mgMatch = comp.match(/(\d+)(mg)/)
          if (mgMatch) {
            return mgMatch[0]
          }

          return ''
        })
        .join(' / ')

      let measure = medicine.qty
      let unitOfMeasure = 'Nos'

      if (typeof measure === 'string' && measure.match(/[a-zA-Z]/)) {
        const match = measure.match(/^(\d+)\s*([a-zA-Z]+)$/)
        if (match) {
          measure = match[1]
          unitOfMeasure = match[2]
        }
      } else if (!isNaN(measure)) {
        unitOfMeasure = 'Nos'
      }

      return {
        id: medicine.id,
        DrugFormulation: medicine.productForm,
        BrandName: medicine.productName,
        GenericName: saltComposition
          .map((comp) => comp.split('(')[0].trim())
          .join(' + '),
        Strength: strength,
        Measure: measure,
        UnitOfMeasure: unitOfMeasure,
        Cost: medicine.mrp,
        productId: medicine.productId,
      }
    })
  }

  transformOrganizationMedicinesData(data) {
    return data.map((medicine) => {
      const saltComposition = medicine.saltComposition?.split(' + ') || []

      const strength = saltComposition
        .map((comp) => {
          const match = comp.match(/\(([^)]+)\)/)
          return match ? match[1] : ''
        })
        .filter((s) => s)
        .join(' + ')

      let measure = medicine.packSize || ''
      let unitOfMeasure = ''

      if (typeof measure === 'string' && measure.trim() !== '') {
        const match = measure.match(/^(\d+)\s*([a-zA-Z]+)$/)
        if (match) {
          measure = match[1]
          unitOfMeasure = match[2]
        }
      } else if (!isNaN(measure)) {
        unitOfMeasure = 'Nos'
      }

      return {
        id: medicine.id,
        DrugFormulation: medicine.productForm,
        BrandName: medicine.productName,
        GenericName: saltComposition
          .map((comp) => comp.split('(')[0].trim())
          .join(' + '),
        Strength: strength,
        Measure: measure,
        UnitOfMeasure: unitOfMeasure,
        Cost: medicine.organizationPrice || 0, // Use organization-specific price
        productId: medicine.productId,
      }
    })
  }

  async seedMedicinesFromExcel() {
    const filePath = getFilePath()
    if (!fs.existsSync(filePath)) {
      throw new Error('Excel file not found')
    }

    const result = excelToJson({
      sourceFile: filePath,
      header: { rows: 1 },
      columnToKey: {
        A: 'productId',
        B: 'productName',
        C: 'marketerOrManufacturer',
        D: 'saltComposition',
        E: 'medicineType',
        F: 'introduction',
        G: 'benefits',
        H: 'description',
        I: 'howToUse',
        J: 'safetyAdvise',
        K: 'ifMiss',
        L: 'packagingDetail',
        M: 'package',
        N: 'qty',
        O: 'productForm',
        P: 'mrp',
        Q: 'prescriptionRequired',
        R: 'factBox',
        S: 'primaryUse',
        T: 'storage',
        U: 'useOf',
        V: 'commonSideEffect',
        W: 'alcoholInteraction',
        X: 'pregnancyInteraction',
        Y: 'lactationInteraction',
        Z: 'drivingInteraction',
        AA: 'kidneyInteraction',
        AB: 'liverInteraction',
        AC: 'manufacturerAddress',
        AD: 'countryOfOrigin',
        AE: 'qa',
        AF: 'howItWorks',
        AG: 'interaction',
        AH: 'manufacturerDetails',
        AI: 'marketerDetails',
        AJ: 'expiration',
        AK: 'reference',
        AL: 'imageUrl',
      },
    })

    const rows = result.Medicines || []
    if (!rows.length) {
      throw new Error('No rows found in the Excel sheet.')
    }

    const existingMedicines = await medicineService.getMedicinesByProductIds(
      rows.map((row) => row.productId),
    )
    const uniqueRows = Array.from(
      new Set(rows.map((row) => row.productId)),
    ).map((productId) => rows.find((row) => row.productId === productId))
    const medicinesToInsert = uniqueRows.filter((row) => {
      const existingMedicine = existingMedicines.find(
        (medicine) => medicine.productId === row.productId,
      )
      return !existingMedicine
    })

    if (!medicinesToInsert.length) {
      return {
        message: 'No new medicines to seed. All medicines already exist.',
      }
    }

    const medicines = medicinesToInsert.map((row) => new MedicineModel(row))
    const inserted = await medicineService.bulkInsertMedicines(medicines)

    return { message: `${inserted.length} medicines seeded successfully.` }
  }

  async searchMedicines(query, pageSize, continueToken) {
    const data = await medicineService.searchMedicine(
      query,
      pageSize,
      continueToken,
    )

    const transformedData = this.transformMedicinesData(data.items)

    return {
      items: transformedData,
      continuationToken: data.continuationToken,
    }
  }

  async searchOrganizationMedicines(
    organizationId,
    query,
    pageSize,
    continueToken,
  ) {
    const data = await medicineService.searchOrganizationMedicines(
      organizationId,
      query,
      pageSize,
      continueToken,
    )

    const transformedData = this.transformOrganizationMedicinesData(data.items)

    return {
      items: transformedData,
      continuationToken: data.continuationToken,
    }
  }

  async fetchMedicinesForOrganization(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const searchText = req.query.get('searchText') || ''
      const pageSize = parseInt(req.query.get('pageSize')) || 100
      const page = parseInt(req.query.get('page')) || 1
      const continuationToken = req.query.get('continuationToken') || null

      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await medicineService.fetchMedicinesForOrganization(
        organizationId,
        searchText,
        pageSize,
        page,
        continuationToken,
      )

      return jsonResponse(result)
    } catch (error) {
      console.error('Error fetching medicines for organization:', error)
      return jsonResponse(
        'Failed to fetch medicines for organization',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateOrganizationMedicines(req) {
    try {
      const body = await req.json()
      const { organizationId, medicines, selectAll } = body

      if (
        !organizationId ||
        (!selectAll && (!Array.isArray(medicines) || medicines.length === 0))
      ) {
        return jsonResponse(
          'Missing required fields: organizationId or medicines when selectAll is false',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await medicineService.updateOrganizationMedicines(
        organizationId,
        medicines,
        selectAll,
      )

      return jsonResponse(result)
    } catch (error) {
      console.error('Error updating organization medicines:', error)
      return jsonResponse(
        'Failed to update organization medicines',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new MedicineHandler()
