const appointmentService = require("../services/appointment-service");
const patientService = require("../services/patient-service");


class AppointmentManageHandler {
    async deleteAppointmentById(appointmentId) {
        return await appointmentService.deleteAppointmentById(appointmentId);
    }

    async getPatientDoctorVisitType(patientId, doctorId) {
        const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.status != 'canceled'`;
        const queues = await appointmentService.getQueueByQuery(query);

        const appointmentIds = queues.map(queue => queue.appointmentId);
        const appointments = await appointmentService.getAppointmentByQuery(`SELECT * FROM c WHERE c.id IN (${appointmentIds.map(id => `'${id}'`).join(", ")}) AND c.doctorId = '${doctorId}'`);
        
        if (appointments && appointments.length > 0) {
            return 'returning';
        }
        return 'new';
    }

    async addAppointment(appointment, created_by) {
        // Format the current date once to reuse for ID generation
        const now = new Date();
        const dateString = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getMilliseconds()).padStart(3, '0')}`;

        const existingAppointments = await appointmentService.getAppointmentByQuery(
            `SELECT * FROM c WHERE c.doctorId = '${appointment.doctorId}' AND c.date = '${appointment.date}'`
        );

        if (!existingAppointments || existingAppointments.length === 0) {
            const appointmentId = `APM${dateString}`;
            const queueId = `QUEUE${dateString}`;

            const apm = {
                id: appointment?.id || appointmentId,
                doctorId: appointment.doctorId,
                date: appointment.date,
                created_by,
                updated_by: created_by,
            };

            const queue = {
                id: queueId,
                appointmentId: appointmentId,
                patientId: appointment.patientId,
                time: appointment.time,
                status: appointment.status,
                queuePosition: 1, // Assuming the position is 1 for new queues
                created_by,
                updated_by: created_by,
            };

            const resApm = await appointmentService.createAppointment(apm);
            const resQueue = await appointmentService.createQueue(queue);
            const data = await this.getAppointmentDetails(apm.id)
            return data
        } else {
            var queues = await appointmentService.getQueueByQuery(`SELECT * FROM c WHERE c.appointmentId = '${existingAppointments[0].id}'`)
            const queueId = `QUEUE${dateString}`;
            const queue = {
                id: queueId,
                appointmentId: existingAppointments[0].id,
                patientId: appointment.patientId,
                time: appointment.time,
                status: appointment.status,
                queuePosition: queues.length + 1,
                created_by,
                updated_by: created_by,
            };

            const resQueue = await appointmentService.createQueue(queue);
            const data = await this.getAppointmentDetails(queue.appointmentId)
            return data

        }
    }

    async getAppointment(doctorId, date) {
        var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}' AND c.date = '${date}'`
        var data = await appointmentService.getAppointmentByQuery(query);
        if (data.length > 0) {
            return data[0]
        }
        return data;
    }

    async getAppointments(doctorId) {
        var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}'`
        var data = await appointmentService.getAppointmentByQuery(query);
        var lst = [];
        for (let i = 0; i < data.length; i++) {
            const e = data[i];
            if (data && data.length > 0) {
                var res = await this.getAppointmentDetails(e.id);
                lst.push(res);
            }
        }
        return lst;
    }

    async updateAppointment(appointment, updated_by) {
        appointment.updated_by = updated_by
        var res = await appointmentService.updateAppointment(appointment);
        return res
    }

    async createQueue(queue, created_by) {
        queue.created_by = created_by
        queue.updated_by = updated_by
        var res = await appointmentService.createQueue(queue);
        return res;

    }

    async getAppointmentDetails(appointmentId) {
        var appointments = await appointmentService.getAppointmentByQuery(`SELECT * FROM c WHERE c.id = '${appointmentId}'`);
        var appointment = appointments[0];
        var queues = await appointmentService.getQueueByQuery(`SELECT * FROM c WHERE c.appointmentId = '${appointmentId}' and c.status != 'canceled'`);
        const patientIds = queues.map(queue => queue.patientId);
        var patients = await patientService.QueryPatientProfile(`SELECT * FROM c WHERE c.id IN (${patientIds.map(id => `'${id}'`).join(", ")})`)
        // Combine results
        const result = {
            appointmentId: appointment.id,
            doctorId: appointment.doctorId,
            date: appointment.date,
            //department: appointment.department || "",
            queues: queues.map(queue => {
                const patient = patients.find(p => p.id === queue.patientId);
                return {
                    queueId: queue.id,
                    patientId: patient.id,
                    patientName: patient.name,
                    patientAge: new Date().getFullYear() - new Date(patient.dob).getFullYear(), // Calculate age
                    patientAddress: patient.address,
                    patientPhone: patient.contact.phone,
                    patientEmail: patient.contact.email,
                    patientSex: patient.sex,
                    patientDoB: patient.dob,
                    time: queue.time,
                    status: queue.status,
                    department: queue.department || "",
                    queuePosition: queue.queuePosition
                };
            })
        };
        return result;
    }

    async upsertAppointment(appointmentId, appointmentData) {
        try {
            var data = await appointmentService.upsertAppointment(appointmentId, appointmentData);
            return data;
        } catch (error) {
            logError(``, error);
            return null
        }
    }

    async updateQueue(queueId, updateData, updated_by) {
        updateData.updated_by = updated_by
        var res = await appointmentService.upsertQueue(queueId, updateData);
        var appointment = await this.getAppointmentDetails(res.appointmentId);
        return appointment;
    }

    async upsertQueueOnly(queueId, updateData, updated_by) {
        updateData.updated_by = updated_by
        var res = await appointmentService.upsertQueue(queueId, updateData);
        return res;
    }

    async getQueueById(queueId) {
        var data = await appointmentService.getQueueById(queueId);
        if (data && data.length > 0) {
            return data[0]
        }
        return data
    }

    async getFutureAppointmentsByPatientId(patientId, now) {
        const nowDate = (typeof now === 'string') ? new Date(now) : now;
        const queues = await appointmentService.getQueueByQuery(`SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.status != 'canceled'`);
        
        const results = [];
        for (const queue of queues) {
            const appointmentArr = await appointmentService.getAppointmentByQuery(`SELECT * FROM c WHERE c.id = '${queue.appointmentId}'`);
            const filterAppointments = appointmentArr.filter(appointment => new Date(appointment.date) > nowDate);
            const appointment = filterAppointments && filterAppointments[0] ? filterAppointments[0] : null;
            if (appointment) {
                results.push({
                    appointmentId: appointment.id,
                    doctorId: appointment.doctorId,
                    date: appointment.date,
                    queueId: queue.id,
                    time: queue.time,
                    status: queue.status,
                    queuePosition: queue.queuePosition,
                    department: queue.department || "",
                });
            }
        }
        return results;
    }
}

module.exports = new AppointmentManageHandler();