// testHandler.js
const fs = require('fs')
const excelToJson = require('convert-excel-to-json')
const TestModel = require('../models/test-model')
const testService = require('../services/lab-test-service')
const { getTestFilePath } = require('../utils/file-utils')
const labTestService = require('../services/lab-test-service') // Assuming a service layer exists
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

class TestHandler {
  async seedTestsFromExcel() {
    const filePath = getTestFilePath()
    if (!fs.existsSync(filePath)) {
      throw new Error('Excel file not found')
    }

    const result = excelToJson({
      sourceFile: filePath,
      header: { rows: 1 },
      columnToKey: {
        A: 'LOINC_NUM',
        B: 'COMPONENT',
        C: 'PROPERTY',
        D: 'TIME_ASPCT',
        E: 'SYSTEM',
        F: 'SCALE_TYP',
        G: 'METHOD_TYP',
        H: 'CLASS',
        I: 'VersionLastChanged',
        J: 'CHNG_TYPE',
        K: 'DefinitionDescription',
        L: 'STATUS',
        M: 'CONSUMER_NAME',
        N: 'CLASSTYPE',
        O: 'FORMULA',
        P: 'EXMPL_ANSWERS',
        Q: 'SURVEY_QUEST_TEXT',
        R: 'SURVEY_QUEST_SRC',
        S: 'UNITSREQUIRED',
        T: 'RELATEDNAMES2',
        U: 'SHORTNAME',
        V: 'ORDER_OBS',
        W: 'HL7_FIELD_SUBFIELD_ID',
        X: 'EXTERNAL_COPYRIGHT_NOTICE',
        Y: 'EXAMPLE_UNITS',
        Z: 'LONG_COMMON_NAME',
        AA: 'EXAMPLE_UCUM_UNITS',
        AB: 'STATUS_REASON',
        AC: 'STATUS_TEXT',
        AD: 'CHANGE_REASON_PUBLIC',
        AE: 'COMMON_TEST_RANK',
        AF: 'COMMON_ORDER_RANK',
        AG: 'HL7_ATTACHMENT_STRUCTURE',
        AH: 'EXTERNAL_COPYRIGHT_LINK',
        AI: 'PanelType',
        AJ: 'AskAtOrderEntry',
        AK: 'AssociatedObservations',
        AL: 'VersionFirstReleased',
        AM: 'ValidHL7AttachmentRequest',
        AN: 'DisplayName',
        AO: 'UNITS',
      },
    })

    const rows = result.Loinc || []
    if (!rows.length) {
      throw new Error('No rows found in the Excel sheet.')
    }

    const loincNums = Array.from(
      new Set(
        rows
          .map((row) => row.LOINC_NUM?.toString().trim())
          .filter((num) => /^[0-9]+-[0-9]+$/.test(num)),
      ),
    )

    if (!loincNums.length) {
      throw new Error('No valid LOINC numbers found.')
    }

    const batchSize = 500
    let existingTests = []

    for (let i = 0; i < loincNums.length; i += batchSize) {
      const batch = loincNums.slice(i, i + batchSize)
      const result = await testService.getTestsByLoincNums(batch)
      existingTests = existingTests.concat(result)
    }
    const testsToInsert = rows.filter((row) => {
      const loinc = row.LOINC_NUM?.toString().trim()
      return (
        loinc &&
        /^[0-9]+-[0-9]+$/.test(loinc) &&
        !existingTests.find((test) => test.LOINC_NUM === loinc)
      )
    })

    if (!testsToInsert.length) {
      return { message: 'No new tests to seed. All tests already exist.' }
    }
    const testModels = testsToInsert.map((row) => new TestModel(row))
    const inserted = await testService.bulkInsertTests(testModels)

    return { message: `${inserted.length} tests seeded successfully.` }
  }

  async getLabTest(req, res) {
    try {
      const labTestId = req.params.id
      if (!labTestId) {
        return res.status(400).json({ message: 'Lab Test ID is required' })
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      if (!labTest) {
        return res.status(404).json({ message: 'Lab Test not found' })
      }

      return res.status(200).json(labTest)
    } catch (error) {
      console.error('Error fetching lab test:', error)
      return res.status(500).json({ message: 'Internal Server Error' })
    }
  }

  async getAllLabTests() {
    try {
      const labTests = await labTestService.getAllLabTests()
      return labTests
    } catch (error) {
      console.error('Error fetching lab tests:', error)
      throw new Error('Failed to fetch lab tests')
    }
  }

  async searchLabTests(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 1000,
        continuationToken = '',
        department = '',
        organizationId = '',
      } = body


      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      let result
      if (organizationId) {
        result = await labTestService.searchOrganizationLabTests(
          organizationId,
          searchText,
          pageSize,
          continuationToken,
          department,
        )
      } else {
        result = await labTestService.searchLabTests(
          searchText,
          pageSize,
          continuationToken,
          department,
        )
      }

      return jsonResponse(result)
    } catch (err) {
      console.error('Search test error:', err)
      return jsonResponse(
        'Error searching lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getDepartments(req, res) {
    try {
      const departments = await labTestService.getDepartments()
      return jsonResponse(departments)
    } catch (error) {
      console.error('Error fetching departments:', error)
      return jsonResponse('Failed to fetch departments', 500)
    }
  }

  async getLoincList(
    searchText,
    department,
    organizationId,
    pageSize,
    continuationToken,
    page,
  ) {
    try {
      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await labTestService.getLoincList(
        searchText,
        department,
        organizationId,
        pageSize,
        continuationToken,
        page,
      )
      return result
    } catch (error) {
      console.error('Error fetching LOINC list:', error)
      throw new Error('Failed to fetch LOINC list')
    }
  }

  async importLoincData(file) {
    try {
      const result = await labTestService.importLoincData(file)
      return result
    } catch (error) {
      console.error('Error importing LOINC data:', error)
      throw new Error('Failed to import LOINC data')
    }
  }

  async updateOrganizationTestDetails(req) {
    try {
      const body = await req.json()
      const {
        organizationId,
        tests,
        department,
        selectAll,
        async: useAsync,
      } = body

      if (
        !organizationId ||
        (!selectAll && (!Array.isArray(tests) || tests.length === 0))
      ) {
        return jsonResponse(
          'Missing required fields: organizationId, tests, or department',
          HttpStatusCode.BadRequest,
        )
      }

      const shouldUseAsync =
        useAsync || (selectAll && department) || (tests && tests.length > 1000)

      if (shouldUseAsync) {
        // Start async processing and return job ID immediately
        const result = await labTestService.startAsyncOrganizationTestsUpdate(
          organizationId,
          tests,
          department,
          selectAll,
        )

        return jsonResponse({
          message: 'Organization test update started',
          jobId: result.jobId,
          async: true,
          statusUrl: `/loinc/update/status/${result.jobId}`,
        })
      } else {
        // Use synchronous processing for small datasets
        const result = await labTestService.updateOrganizationTests(
          organizationId,
          tests,
          department,
          selectAll,
        )

        return jsonResponse({
          message: 'Organization test details updated successfully',
          updatedCount: result.length,
          async: false,
        })
      }
    } catch (error) {
      console.error('Error updating organization test details:', error)
      return jsonResponse(
        'Failed to update organization test details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getUpdateJobStatus(req) {
    try {
      const jobId = req.params.jobId

      if (!jobId) {
        return jsonResponse(
          'Missing required parameter: jobId',
          HttpStatusCode.BadRequest,
        )
      }

      const status = labTestService.getJobStatus(jobId)

      if (status.error) {
        return jsonResponse(status.error, HttpStatusCode.NotFound)
      }

      return jsonResponse(status)
    } catch (error) {
      console.error('Error getting job status:', error)
      return jsonResponse(
        'Failed to get job status',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async fetchLoincTestsForOrganization(req) {
    try {
      const organizationId = req.query.get('organizationId')
      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await labTestService.fetchLoincTestsForOrganization(
        organizationId,
      )

      return jsonResponse(result)
    } catch (error) {
      console.error('Error fetching LOINC tests for organization:', error)
      return jsonResponse(
        'Failed to fetch LOINC tests for organization',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new TestHandler()
