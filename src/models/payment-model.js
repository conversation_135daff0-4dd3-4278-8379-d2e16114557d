const { v4: uuidv4 } = require('uuid')

/**
 * Payment Model for EMR System
 * Represents payment transactions processed through Razorpay
 */
class PaymentModel {
  constructor(data = {}) {
    this.id = data.id || uuidv4()
    this.razorpayOrderId = data.razorpayOrderId || null
    this.razorpayPaymentId = data.razorpayPaymentId || null
    this.razorpaySignature = data.razorpaySignature || null
    this.amount = data.amount || 0
    this.currency = data.currency || 'INR'
    this.receipt = data.receipt || null
    this.status = data.status || 'created' // created, completed, failed, captured, paid
    this.notes = data.notes || {}
    this.failureReason = data.failureReason || null
    this.createdAt = data.createdAt || new Date().toISOString()
    this.updatedAt = data.updatedAt || new Date().toISOString()
    this.verifiedAt = data.verifiedAt || null
    this.capturedAt = data.capturedAt || null
    this.failedAt = data.failedAt || null
    this.paidAt = data.paidAt || null
  }

  /**
   * Validate payment data
   * @returns {Object} Validation result
   */
  validate() {
    const errors = []

    if (!this.amount || this.amount <= 0) {
      errors.push('Amount must be greater than 0')
    }

    if (!this.currency) {
      errors.push('Currency is required')
    }

    if (!this.receipt) {
      errors.push('Receipt is required')
    }

    if (!this.notes.patientId) {
      errors.push('Patient ID is required in notes')
    }

    if (!this.notes.organizationId) {
      errors.push('Organization ID is required in notes')
    }

    const validStatuses = ['created', 'completed', 'failed', 'captured', 'paid']
    if (!validStatuses.includes(this.status)) {
      errors.push(`Status must be one of: ${validStatuses.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
    }
  }

  /**
   * Convert to JSON for API responses
   * @returns {Object} JSON representation
   */
  toJSON() {
    return {
      id: this.id,
      razorpayOrderId: this.razorpayOrderId,
      razorpayPaymentId: this.razorpayPaymentId,
      amount: this.amount,
      currency: this.currency,
      receipt: this.receipt,
      status: this.status,
      notes: this.notes,
      failureReason: this.failureReason,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      verifiedAt: this.verifiedAt,
      capturedAt: this.capturedAt,
      failedAt: this.failedAt,
      paidAt: this.paidAt,
    }
  }

  /**
   * Create payment from Razorpay order
   * @param {Object} razorpayOrder - Razorpay order object
   * @param {Object} notes - Additional notes/metadata
   * @returns {PaymentModel} Payment instance
   */
  static fromRazorpayOrder(razorpayOrder, notes = {}) {
    return new PaymentModel({
      razorpayOrderId: razorpayOrder.id,
      amount: razorpayOrder.amount / 100, // Convert from paise to rupees
      currency: razorpayOrder.currency,
      receipt: razorpayOrder.receipt,
      status: 'created',
      notes: notes,
    })
  }

  /**
   * Update payment status
   * @param {string} status - New status
   * @param {Object} additionalData - Additional data to update
   */
  updateStatus(status, additionalData = {}) {
    this.status = status
    this.updatedAt = new Date().toISOString()

    // Set timestamp based on status
    switch (status) {
      case 'completed':
        this.verifiedAt = additionalData.verifiedAt || new Date().toISOString()
        break
      case 'captured':
        this.capturedAt = additionalData.capturedAt || new Date().toISOString()
        break
      case 'failed':
        this.failedAt = additionalData.failedAt || new Date().toISOString()
        this.failureReason = additionalData.failureReason || 'Payment failed'
        break
      case 'paid':
        this.paidAt = additionalData.paidAt || new Date().toISOString()
        break
    }

    // Update other fields
    Object.keys(additionalData).forEach(key => {
      if (key !== 'verifiedAt' && key !== 'capturedAt' && key !== 'failedAt' && key !== 'paidAt') {
        this[key] = additionalData[key]
      }
    })
  }

  /**
   * Check if payment is successful
   * @returns {boolean} True if payment is successful
   */
  isSuccessful() {
    return ['completed', 'captured', 'paid'].includes(this.status)
  }

  /**
   * Check if payment is failed
   * @returns {boolean} True if payment is failed
   */
  isFailed() {
    return this.status === 'failed'
  }

  /**
   * Check if payment is pending
   * @returns {boolean} True if payment is pending
   */
  isPending() {
    return this.status === 'created'
  }

  /**
   * Get payment summary for display
   * @returns {Object} Payment summary
   */
  getSummary() {
    return {
      id: this.id,
      amount: this.amount,
      currency: this.currency,
      status: this.status,
      patientId: this.notes.patientId,
      organizationId: this.notes.organizationId,
      appointmentId: this.notes.appointmentId,
      description: this.notes.description,
      createdAt: this.createdAt,
      isSuccessful: this.isSuccessful(),
      isFailed: this.isFailed(),
      isPending: this.isPending(),
    }
  }
}

/**
 * Payment Statistics Model
 * Represents aggregated payment statistics for organizations
 */
class PaymentStatsModel {
  constructor(data = {}) {
    this.organizationId = data.organizationId || null
    this.totalPayments = data.totalPayments || 0
    this.totalAmount = data.totalAmount || 0
    this.completedPayments = data.completedPayments || 0
    this.failedPayments = data.failedPayments || 0
    this.pendingPayments = data.pendingPayments || 0
    this.averageAmount = data.averageAmount || 0
    this.successRate = data.successRate || 0
    this.generatedAt = data.generatedAt || new Date().toISOString()
  }

  /**
   * Calculate statistics from payment list
   * @param {Array} payments - List of payments
   * @param {string} organizationId - Organization ID
   * @returns {PaymentStatsModel} Statistics instance
   */
  static fromPayments(payments, organizationId) {
    const totalPayments = payments.length
    const totalAmount = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0)
    const completedPayments = payments.filter(p => ['completed', 'captured', 'paid'].includes(p.status)).length
    const failedPayments = payments.filter(p => p.status === 'failed').length
    const pendingPayments = payments.filter(p => p.status === 'created').length
    const averageAmount = totalPayments > 0 ? totalAmount / totalPayments : 0
    const successRate = totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0

    return new PaymentStatsModel({
      organizationId,
      totalPayments,
      totalAmount,
      completedPayments,
      failedPayments,
      pendingPayments,
      averageAmount: Math.round(averageAmount * 100) / 100, // Round to 2 decimal places
      successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
    })
  }

  /**
   * Convert to JSON for API responses
   * @returns {Object} JSON representation
   */
  toJSON() {
    return {
      organizationId: this.organizationId,
      totalPayments: this.totalPayments,
      totalAmount: this.totalAmount,
      completedPayments: this.completedPayments,
      failedPayments: this.failedPayments,
      pendingPayments: this.pendingPayments,
      averageAmount: this.averageAmount,
      successRate: this.successRate,
      generatedAt: this.generatedAt,
    }
  }
}

/**
 * Payment Order Request Model
 * Represents a request to create a payment order
 */
class PaymentOrderRequestModel {
  constructor(data = {}) {
    this.amount = data.amount || 0
    this.currency = data.currency || 'INR'
    this.patientId = data.patientId || null
    this.organizationId = data.organizationId || null
    this.appointmentId = data.appointmentId || null
    this.description = data.description || 'EMR Payment'
    this.metadata = data.metadata || {}
  }

  /**
   * Validate payment order request
   * @returns {Object} Validation result
   */
  validate() {
    const errors = []

    if (!this.amount || this.amount <= 0) {
      errors.push('Amount must be greater than 0')
    }

    if (!this.patientId) {
      errors.push('Patient ID is required')
    }

    if (!this.organizationId) {
      errors.push('Organization ID is required')
    }

    if (!this.currency) {
      errors.push('Currency is required')
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
    }
  }
}

module.exports = {
  PaymentModel,
  PaymentStatsModel,
  PaymentOrderRequestModel,
}
