const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')
class PatientModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.name = data.name || ''
    this.sex = data.sex || ''
    this.dob = data.dob || ''
    this.height = data.height || ''
    this.weight = data.weight || ''
    this.address = data.address || ''
    this.aadhar = data.aadhar || ''
    this.abha = data.abha || ''
    this.contact = {
      phone: data.contact?.phone || '',
      email: data.contact?.email || '',
    }
    this.insurance = {
      provider: data.insurance?.provider || '',
      id: data.insurance?.id || '',
      proof: data.insurance?.proof || '',
    }
    this.id = data.id || ''
    this.organizationId = data.organizationId || ''
    this.last_consultation_date = data.last_consultation_date || null
    this.idProof = data.idProof || ''
    this.city = data.city || ''
    this.state = data.state || ''
    this.age = data.age || ''
  }
}

module.exports = PatientModel
