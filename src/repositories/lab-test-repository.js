const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const {
  getTestsByLoincNums,
  getAllLabTestsQuery,
  searchLabTestsQuery,
  getAllDistinctClassValuesQuery,
  getOrganizationTestIdsWithPricingQuery,
  searchLabTestsByIdsQuery,
} = require('../queries/lab-test-query')
const logging = require('../common/logging')
const {
  classToDepartmentMapping,
  getDepartmentByClassCode,
} = require('../common/class-department-mapping')
const labTestContainer = 'lab_tests'
const organizationTestsContainer = 'OrganizationTests'

const {
  sanitizeInput,
  sanitizeSearchText,
  escapeForCosmosDB,
} = require('../utils/sanitization')

class TestRepository {
  async findByLoincNums(loincNums) {
    try {
      const query = getTestsByLoincNums(loincNums)
      return cosmosDbContext.queryItems(query, labTestContainer)
    } catch (error) {
      logging.logError('Error fetching lab tests by LOINC numbers', error)
      throw new Error('Failed to fetch lab tests by LOINC numbers')
    }
  }

  async bulkInsertTest(tests) {
    try {
      const insertResults = []
      for (const test of tests) {
        const result = await cosmosDbContext.createItem(test, labTestContainer)
        insertResults.push(result)
      }
      return insertResults
    } catch (error) {
      logging.logError('Error inserting lab tests', error)
      throw new Error('Failed to insert lab tests')
    }
  }

  async getAllLabTests() {
    try {
      const query = getAllLabTestsQuery()
      return cosmosDbContext.queryItems(query, labTestContainer)
    } catch (error) {
      logging.logError('Error fetching all lab tests', error)
      throw new Error('Failed to fetch lab tests')
    }
  }

  async searchLabTests(
    searchText,
    pageSize = 10,
    continuationToken = null,
    department,
  ) {
    try {
      function sortSearchResults(items, searchText) {
        if (!searchText) return items
        const lowerSearch = searchText.toLowerCase()

        return items.sort((a, b) => {
          const aName =
            a.DisplayName?.toLowerCase() ||
            a.SHORTNAME?.toLowerCase() ||
            a.LONG_COMMON_NAME?.toLowerCase() ||
            ''
          const bName =
            b.DisplayName?.toLowerCase() ||
            b.SHORTNAME?.toLowerCase() ||
            b.LONG_COMMON_NAME?.toLowerCase() ||
            ''

          const aStarts = aName.startsWith(lowerSearch) ? 0 : 1
          const bStarts = bName.startsWith(lowerSearch) ? 0 : 1

          if (aStarts !== bStarts) return aStarts - bStarts
          return aName.localeCompare(bName)
        })
      }

      let classFilter = []
      const fetchSize = 5000
      if (department === 'ALL') {
        classFilter = null
      } else if (department === 'OTHERS') {
        const allClasses = await this.getAllClassValues()
        const knownClasses = Object.keys(classToDepartmentMapping)
        classFilter = allClasses.filter((cls) => !knownClasses.includes(cls))
      } else {
        classFilter = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)
      }

      const query = searchLabTestsQuery(searchText, classFilter)

      const data = await cosmosDbContext.getAllItemQuery(
        labTestContainer,
        query,
        fetchSize,
        continuationToken,
      )

      // If no results but there's a continuation token, try fetching next page
      if (data.items.length === 0 && data.nextToken) {
        const nextPageData = await cosmosDbContext.getAllItemQuery(
          labTestContainer,
          query,
          fetchSize,
          data.nextToken,
        )

        // If we found results on the next page, use those
        if (nextPageData.items.length > 0) {
          data.items = nextPageData.items
          data.nextToken = nextPageData.nextToken
        }
      }

      // If still no results, try a simpler query as fallback
      if (data.items.length === 0 && searchText) {
        const simpleQuery = `SELECT * FROM c WHERE CONTAINS(UPPER(c.DisplayName), UPPER("${searchText.replace(
          /"/g,
          '',
        )}")) OR CONTAINS(UPPER(c.SHORTNAME), UPPER("${searchText.replace(
          /"/g,
          '',
        )}")) OR CONTAINS(UPPER(c.LONG_COMMON_NAME), UPPER("${searchText.replace(
          /"/g,
          '',
        )}"))`

        const simpleData = await cosmosDbContext.getAllItemQuery(
          labTestContainer,
          simpleQuery,
          fetchSize,
          null,
        )

        if (simpleData.items.length > 0) {
          data.items = simpleData.items
          data.nextToken = simpleData.nextToken
        }
      }

      const sortedItems = sortSearchResults(data.items, searchText)

      return {
        items: sortedItems,
        continuationToken: data.continuationToken,
      }
    } catch (error) {
      logging.logError(`Unable to search test with query`, error)
      return { items: [], nextToken: null }
    }
  }
  async getAllClassValues() {
    try {
      const query = getAllDistinctClassValuesQuery()
      const result = await cosmosDbContext.queryItems(query, labTestContainer)

      return result.map((item) => item.CLASS)
    } catch (error) {
      console.error('Error fetching class values from Cosmos DB:', error)
      throw new Error('Failed to fetch class values')
    }
  }

  async fetchLoincList(
    searchText,
    department,
    organizationId,
    pageSize = 100,
    continuationToken = null,
    page = 1,
  ) {
    try {
      const sanitizedSearchText = sanitizeSearchText(searchText)
      const sanitizedDepartment = sanitizeInput(department)

      let actualPage = page
      if (continuationToken && continuationToken.startsWith('page_')) {
        actualPage = parseInt(continuationToken.replace('page_', ''))
      }

      // Step 1: Get organization tests map for efficient lookup (only once)
      const orgTestsMap = await this.getOrganizationTestsMap(organizationId)

      const countQuery = this.buildCountQuery(
        sanitizedSearchText,
        sanitizedDepartment,
      )

      const countResult = await cosmosDbContext.queryItems(
        countQuery,
        labTestContainer,
      )
      const totalCount = countResult[0] || 0
      const totalPages = Math.ceil(totalCount / pageSize)

      const loincQuery = this.buildOptimizedLoincQuery(
        sanitizedSearchText,
        sanitizedDepartment,
        pageSize,
        actualPage,
      )

      const allResults = await cosmosDbContext.queryItems(
        loincQuery,
        labTestContainer,
      )

      const hasMoreResults = allResults.length > pageSize
      const actualResults = hasMoreResults
        ? allResults.slice(0, pageSize)
        : allResults

      const nextPageToken = hasMoreResults ? `page_${actualPage + 1}` : null

      const transformedItems = actualResults.map((loincTest, index) => {
        const orgData = orgTestsMap.get(loincTest.id) || {
          isActive: false,
          price: 0,
        }

        return {
          loincNum: loincTest.id,
          class: loincTest.CLASS,
          shortName: loincTest.SHORTNAME,
          longCommonName: loincTest.LONG_COMMON_NAME,
          displayName: loincTest.DisplayName,
          isActive: orgData.isActive,
          cost: 0,
          organizationCost: orgData.price,
        }
      })

      const response = {
        items: transformedItems,
        continuationToken: nextPageToken,
        hasMoreResults: hasMoreResults,
        currentPage: actualPage,
        pageSize: pageSize,
        totalFetched: actualResults.length,
        totalCount: totalCount,
        totalPages: totalPages,
      }

      return response
    } catch (error) {
      logging.logError('Error fetching LOINC list:', error)
      throw new Error('Failed to fetch LOINC list')
    }
  }

  buildQueryConditions(searchText, department) {
    const conditions = []

    if (searchText && searchText.trim() !== '') {
      // Use improved sanitization for search text
      const cleanSearchText = sanitizeSearchText(searchText)
      if (cleanSearchText) {
        // Escape the search text for safe use in Cosmos DB queries
        const escapedSearchText = escapeForCosmosDB(cleanSearchText)

        const searchCondition = `(
          CONTAINS(UPPER(c.SHORTNAME), UPPER("${escapedSearchText}")) OR
          CONTAINS(UPPER(c.LONG_COMMON_NAME), UPPER("${escapedSearchText}")) OR
          CONTAINS(UPPER(c.DisplayName), UPPER("${escapedSearchText}")) OR
          CONTAINS(UPPER(c.id), UPPER("${escapedSearchText}"))
        )`
        conditions.push(searchCondition)
      }
    }

    if (department && department !== 'ALL') {
      if (department === 'OTHERS') {
        const knownClasses = Object.keys(classToDepartmentMapping)
        const excludeCondition = knownClasses
          .map((cls) => `c.CLASS != "${cls}"`)
          .join(' AND ')
        conditions.push(`(${excludeCondition})`)
      } else {
        const departmentClasses = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)

        if (departmentClasses.length > 0) {
          const classCondition = departmentClasses
            .map((cls) => `c.CLASS = "${cls}"`)
            .join(' OR ')
          conditions.push(`(${classCondition})`)
        }
      }
    }

    return conditions
  }

  // Build count query to get total number of records
  buildCountQuery(searchText, department) {
    let query = `SELECT VALUE COUNT(1) FROM c`
    const conditions = this.buildQueryConditions(searchText, department)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    return query
  }

  buildOptimizedLoincQuery(searchText, department, pageSize, page) {
    let query = `SELECT c.id, c.CLASS, c.SHORTNAME, c.LONG_COMMON_NAME, c.DisplayName FROM c`
    const conditions = this.buildQueryConditions(searchText, department)

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`
    }

    query += ` ORDER BY c.id`

    const offset = (page - 1) * pageSize
    query += ` OFFSET ${offset} LIMIT ${pageSize + 1}` // +1 to check if there are more results

    return query
  }

  async getOrganizationTestsMap(organizationId) {
    try {
      const query = `SELECT c.testId, c.isActive, c.price FROM c WHERE c.organizationId = "${organizationId}"`
      const orgTests = await cosmosDbContext.queryItems(
        query,
        organizationTestsContainer,
      )

      if (orgTests.length > 0) {
        console.log('Sample organization tests:', orgTests.slice(0, 3))
      }

      const orgTestsMap = new Map()
      orgTests.forEach((test) => {
        orgTestsMap.set(test.testId, {
          isActive: test.isActive || false,
          price: test.price || 0,
        })
      })
      return orgTestsMap
    } catch (error) {
      logging.logError('Error fetching organization tests map:', error)
      return new Map()
    }
  }

  async bulkInsertLoincData(data) {
    try {
      const results = []
      for (const item of data) {
        const result = await cosmosDbContext.createItem(item, labTestContainer)
        results.push(result)
      }
      return results
    } catch (error) {
      logging.logError('Error inserting LOINC data', error)
      throw new Error('Failed to insert LOINC data')
    }
  }

  async updateOrganizationTests(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      const updatedTests = []

      let testsToUpdate = tests

      if (selectAll && department) {
        logging.logInfo(
          'Fetching tests for selectAll and department using optimized query',
        )

        // Use optimized query with department filter instead of fetching all test data
        const departmentClasses = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)

        if (departmentClasses.length === 0) {
          throw new Error(`No classes found for department: ${department}`)
        }

        const classCondition = departmentClasses
          .map((cls) => `c.CLASS = "${cls}"`)
          .join(' OR ')

        // Only select the ID field to minimize data transfer
        const optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`

        console.log(`Optimized selectAll query: ${optimizedQuery}`)

        const allTests = await cosmosDbContext.queryItems(
          optimizedQuery,
          labTestContainer,
        )

        console.log(
          `Found ${allTests.length} tests for department ${department}`,
        )

        testsToUpdate = allTests.map((test) => ({
          testId: test.id,
          isActive: true,
        }))
      }

      const existingTestsQuery = `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`
      const existingTests = await cosmosDbContext.queryItems(
        existingTestsQuery,
        organizationTestsContainer,
      )
      const existingTestIds = new Set(existingTests.map((test) => test.testId))

      const newTests = []
      const updatedExistingTests = []

      for (const test of testsToUpdate) {
        const { testId, isActive, price } = test

        if (existingTestIds.has(testId)) {
          const existingTest = existingTests.find((t) => t.testId === testId)
          existingTest.isActive = isActive
          if (!selectAll) {
            existingTest.price = price // Update price only when selectAll is false
          }
          existingTest.updatedOn = new Date().toISOString()
          updatedExistingTests.push(existingTest)
        } else {
          const newTest = {
            organizationId,
            testId,
            isActive: true,
            price: price || 0,
            departments: [],
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          }
          newTests.push(newTest)
        }
      }

      const envConfig = cosmosDbContext.getEnvironmentConfig()
      const batchSize = envConfig.batchSize // 500 for dev, 1000 for prod
      const maxConcurrency = envConfig.maxConcurrency // 6 for dev, 10 for prod

      logging.logInfo(
        `Using ${envConfig.environment} config: batchSize=${batchSize}, maxConcurrency=${maxConcurrency}`,
      )

      logging.logInfo(
        `Processing ${updatedExistingTests.length} existing tests in optimized batches`,
      )

      // Process existing tests updates in optimized batches
      for (let i = 0; i < updatedExistingTests.length; i += batchSize) {
        const batch = updatedExistingTests.slice(i, i + batchSize)

        // Process in smaller concurrent chunks to avoid rate limits
        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.updateItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        console.log(
          `Processed ${Math.min(i + batchSize, updatedExistingTests.length)}/${
            updatedExistingTests.length
          } existing tests`,
        )

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < updatedExistingTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200)) // 200ms delay for metadata safety
        }
      }

      logging.logInfo(
        `Processing ${newTests.length} new tests in optimized batches`,
      )

      // Process new tests in optimized batches
      for (let i = 0; i < newTests.length; i += batchSize) {
        const batch = newTests.slice(i, i + batchSize)

        // Process in smaller concurrent chunks to avoid rate limits
        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.createItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        console.log(
          `Processed ${Math.min(i + batchSize, newTests.length)}/${
            newTests.length
          } new tests`,
        )

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < newTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200)) // 200ms delay for metadata safety
        }
      }

      logging.logInfo('Finished updateOrganizationTests method')
      return updatedTests
    } catch (error) {
      logging.logError('Error updating organization test details', error)
      throw new Error('Failed to update organization test details')
    }
  }

  // Optimized async version with progress tracking
  async updateOrganizationTestsAsync(
    organizationId,
    tests,
    department = null,
    selectAll = false,
    progressCallback = null,
  ) {
    try {
      const updatedTests = []
      let testsToUpdate = tests

      // Step 1: Get tests to update (optimized for selectAll)
      if (selectAll && department) {
        if (progressCallback)
          progressCallback(0, 0, 'Fetching tests for department...')

        const departmentClasses = Object.entries(classToDepartmentMapping)
          .filter(([, dept]) => dept === department)
          .map(([classCode]) => classCode)

        if (departmentClasses.length === 0) {
          throw new Error(`No classes found for department: ${department}`)
        }

        const classCondition = departmentClasses
          .map((cls) => `c.CLASS = "${cls}"`)
          .join(' OR ')

        // Only select the ID field to minimize data transfer
        const optimizedQuery = `SELECT c.id FROM c WHERE (${classCondition})`

        const allTests = await cosmosDbContext.queryItems(
          optimizedQuery,
          labTestContainer,
        )

        testsToUpdate = allTests.map((test) => ({
          testId: test.id,
          isActive: true,
        }))

        if (progressCallback) {
          progressCallback(
            0,
            testsToUpdate.length,
            `Found ${testsToUpdate.length} tests for ${department}`,
          )
        }
      }

      // Step 2: Get existing tests
      if (progressCallback)
        progressCallback(
          0,
          testsToUpdate.length,
          'Loading existing organization tests...',
        )

      const existingTests = await cosmosDbContext.queryItems(
        `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`,
        organizationTestsContainer,
      )
      const existingTestIds = new Set(existingTests.map((test) => test.testId))

      // Step 3: Categorize tests
      const newTests = []
      const updatedExistingTests = []

      for (const test of testsToUpdate) {
        const { testId, isActive, price } = test

        if (existingTestIds.has(testId)) {
          const existingTest = existingTests.find((t) => t.testId === testId)
          existingTest.isActive = isActive
          if (!selectAll) {
            existingTest.price = price
          }
          existingTest.updatedOn = new Date().toISOString()
          updatedExistingTests.push(existingTest)
        } else {
          const newTest = {
            organizationId,
            testId,
            isActive: true,
            price: price || 0,
            departments: [],
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          }
          newTests.push(newTest)
        }
      }

      const totalOperations = updatedExistingTests.length + newTests.length
      let processedOperations = 0

      // Step 4: Process existing tests with environment-aware configuration
      const envConfig = cosmosDbContext.getEnvironmentConfig()
      const batchSize = envConfig.batchSize // 500 for dev, 1000 for prod
      const maxConcurrency = envConfig.maxConcurrency // 6 for dev, 10 for prod

      logging.logInfo(
        `Async processing with ${envConfig.environment} config: batchSize=${batchSize}, maxConcurrency=${maxConcurrency}`,
      )

      for (let i = 0; i < updatedExistingTests.length; i += batchSize) {
        const batch = updatedExistingTests.slice(i, i + batchSize)

        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.updateItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        processedOperations += batch.length

        if (progressCallback) {
          progressCallback(
            processedOperations,
            totalOperations,
            `Updated ${processedOperations}/${totalOperations} tests`,
          )
        }

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < updatedExistingTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      // Step 5: Process new tests with progress tracking
      for (let i = 0; i < newTests.length; i += batchSize) {
        const batch = newTests.slice(i, i + batchSize)

        const chunks = []
        const chunkSize = Math.ceil(batch.length / maxConcurrency)

        for (let j = 0; j < batch.length; j += chunkSize) {
          chunks.push(batch.slice(j, j + chunkSize))
        }

        await Promise.all(
          chunks.map(async (chunk) => {
            await Promise.all(
              chunk.map((test) =>
                cosmosDbContext.createItem(test, organizationTestsContainer),
              ),
            )
          }),
        )

        updatedTests.push(...batch)
        processedOperations += batch.length

        if (progressCallback) {
          progressCallback(
            processedOperations,
            totalOperations,
            `Processed ${processedOperations}/${totalOperations} tests`,
          )
        }

        // Add delay between batches to avoid metadata-429 errors
        if (i + batchSize < newTests.length) {
          await new Promise((resolve) => setTimeout(resolve, 200))
        }
      }

      if (progressCallback) {
        progressCallback(
          totalOperations,
          totalOperations,
          'All tests processed successfully!',
        )
      }

      return {
        totalProcessed: updatedTests.length,
        newTests: newTests.length,
        updatedTests: updatedExistingTests.length,
        tests: updatedTests,
      }
    } catch (error) {
      logging.logError('Error in async organization tests update:', error)
      throw error
    }
  }

  async fetchLoincTestsForOrganization(organizationId) {
    try {
      const loincTestsQuery = `SELECT * FROM c`
      const loincTests = await cosmosDbContext.queryItems(
        loincTestsQuery,
        labTestContainer,
      )

      const organizationTests = await cosmosDbContext.queryItems(
        `SELECT * FROM c WHERE c.organizationId = "${organizationId}"`,
        organizationTestsContainer,
      )

      const mergedTests = loincTests.map((loincTest) => {
        const orgTest = organizationTests.find(
          (test) => test.testId === loincTest.id,
        )
        return {
          ...loincTest,
          isActive: orgTest?.isActive || false,
          price: orgTest?.price || null,
          departments: orgTest?.departments || [],
        }
      })

      return mergedTests
    } catch (error) {
      logging.logError('Error fetching LOINC tests for organization', error)
      throw new Error('Failed to fetch LOINC tests for organization')
    }
  }

  async getOrganizationTestIdsWithPricing(organizationId) {
    try {
      const query = getOrganizationTestIdsWithPricingQuery(organizationId)

      const result = await cosmosDbContext.queryItems(
        query,
        organizationTestsContainer,
      )

      return result
    } catch (error) {
      logging.logError(
        `Error fetching organization test IDs with pricing for org ${organizationId}:`,
        error,
      )
      return []
    }
  }

  async searchLabTestsByIds(
    testIds,
    searchText,
    classFilter,
    pageSize,
    continuationToken,
  ) {
    try {
      const query = searchLabTestsByIdsQuery(testIds, searchText, classFilter)

      const result = await cosmosDbContext.getAllItemQuery(
        labTestContainer,
        query,
        pageSize,
        continuationToken,
      )

      return result
    } catch (error) {
      logging.logError(
        `Error searching lab tests by IDs with search text "${searchText}":`,
        error,
      )
      return { items: [], continuationToken: null }
    }
  }
}

module.exports = new TestRepository()
