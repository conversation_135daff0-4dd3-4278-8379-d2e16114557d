const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const { paginate } = require('../utils/pagination')
const logging = require('../common/logging')
const { getPatientsByOrganizationQuery } = require('../queries/patient-query')

const patientContainer = 'PatientProfiles'

class PatientRepository {
  async fetchPatientsForOrganization(
    organizationId,
    searchText,
    filters,
    sortBy,
    sortOrder,
    pageSize,
    page,
  ) {
    try {
      const query = getPatientsByOrganizationQuery(organizationId)
      const patients = await cosmosDbContext.queryItems(query, patientContainer)

      let filteredPatients = patients

      if (searchText) {
        const lowerSearchText = searchText.toLowerCase()
        filteredPatients = filteredPatients.filter(
          (patient) =>
            patient.id?.toLowerCase().includes(lowerSearchText) ||
            patient.name?.toLowerCase().includes(lowerSearchText) ||
            patient.contact?.phone?.toLowerCase().includes(lowerSearchText),
        )
      }

      if (filters.gender) {
        filteredPatients = filteredPatients.filter(
          (patient) => patient.sex === filters.gender,
        )
      }
      if (filters.ageRange) {
        const [minAge, maxAge] = filters.ageRange
        filteredPatients = filteredPatients.filter(
          (patient) => patient.age >= minAge && patient.age <= maxAge,
        )
      }
      if (filters.registrationDateRange) {
        const [startDate, endDate] = filters.registrationDateRange
        filteredPatients = filteredPatients.filter(
          (patient) =>
            new Date(patient.created_on) >= new Date(startDate) &&
            new Date(patient.created_on) <= new Date(endDate),
        )
      }
      if (filters.fromAge !== undefined && filters.toAge !== undefined) {
        const { fromAge, toAge } = filters
        filteredPatients = filteredPatients.filter(
          (patient) => patient.age >= fromAge && patient.age <= toAge,
        )
      }
      if (filters.fromDate && filters.toDate) {
        const { fromDate, toDate } = filters
        filteredPatients = filteredPatients.filter(
          (patient) =>
            new Date(patient.created_on) >= new Date(fromDate) &&
            new Date(patient.created_on) <= new Date(toDate),
        )
      }

      if (sortBy) {
        filteredPatients.sort((a, b) => {
          const valueA = a[sortBy]?.toLowerCase() || ''
          const valueB = b[sortBy]?.toLowerCase() || ''
          if (sortOrder === 'desc') {
            return valueB.localeCompare(valueA)
          }
          return valueA.localeCompare(valueB)
        })
      }

      const paginatedResults = paginate(filteredPatients, pageSize, page)

      return {
        items: paginatedResults.items,
        totalItemCount: filteredPatients.length,
        currentPage: paginatedResults.currentPage,
        totalPages: Math.ceil(filteredPatients.length / pageSize),
      }
    } catch (error) {
      logging.logError(
        `Failed to fetch patients for organization: ${error.message}`,
        error,
      )
      throw new Error('Failed to fetch patients for organization')
    }
  }
}

module.exports = new PatientRepository()
