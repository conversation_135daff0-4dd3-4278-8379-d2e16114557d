const {
  getLabTestsByPatientIdQuery,
  getLabTestById,
  searchTestQuery,
  getLabTestsWithSortingAndFilteringQuery,
} = require('../queries/patient-lab-test-query')
const { buildDateFilterClause } = require('../utils/query-utils')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const { LabTestStatus } = require('../common/constant')
const {
  getDepartmentByClassCode,
} = require('../common/class-department-mapping')
const patientLabTestsContainer = 'PatientLabTests'
const metadataContainer = 'lab_reports-meta-data'
const labTestsContainer = 'lab_tests'

class LabTestRepository {
  async getLabTestsByPatientId(patientId) {
    const query = getLabTestsByPatientIdQuery(patientId)
    return cosmosDbContext.queryItems(query, patientLabTestsContainer)
  }

  async getLabTestsByPatient(
    patientId,
    dateFilter,
    sortField,
    sortOrder,
    customDateRange,
    searchText,
    department,
  ) {
    try {
      let dateClause = ''
      if (dateFilter === 'custom' && customDateRange) {
        customDateRange.end =
          new Date(customDateRange.end).toISOString().split('T')[0] +
          'T23:59:59.999Z'
        dateClause = `AND c.created_on >= "${customDateRange.start}" AND c.created_on <= "${customDateRange.end}"`
      } else {
        dateClause = buildDateFilterClause(dateFilter)
      }

      const getLabTestsWithSortingAndFilteringQuery = (
        patientId,
        dateClause,
        sortClause,
        searchText,
      ) => {
        const searchClause = searchText
          ? `AND EXISTS(
            SELECT VALUE t FROM t IN c.labTests 
            WHERE CONTAINS(LOWER(t.testName), LOWER("${searchText}"))
          )`
          : ''
        return `
        SELECT * FROM c
        WHERE c.patientId = "${patientId}"
        ${dateClause}
        ${searchClause}
        ${sortClause}
      `
      }

      const sortClause =
        sortField && !['testName', 'department'].includes(sortField)
          ? `ORDER BY c.${sortField} ${sortOrder === 'desc' ? 'DESC' : 'ASC'}`
          : 'ORDER BY c.updated_on DESC'

      const query = getLabTestsWithSortingAndFilteringQuery(
        patientId,
        dateClause,
        sortClause,
        searchText,
      )

      const labTestsData = await cosmosDbContext.queryItems(
        query,
        patientLabTestsContainer,
      )

      const enrichedLabTestsData = await Promise.all(
        labTestsData.map(async (labTestData) => {
          const labTestFileMetadata = await cosmosDbContext.queryItems(
            `SELECT * FROM c WHERE c.labTestId = "${labTestData.id}"`,
            metadataContainer,
          )

          const updatedLabTests = await Promise.all(
            labTestData.labTests.map(async (test) => {
              const classQuery = `SELECT c.CLASS FROM c WHERE c.id = "${test.testId}"`
              const classResult = await cosmosDbContext.queryItems(
                classQuery,
                labTestsContainer,
              )
              const testClass = classResult[0]?.CLASS || 'Unknown'
              const testDepartment = getDepartmentByClassCode(testClass)

              const testFileMetadata = labTestFileMetadata.filter(
                (metadata) => metadata.labTestId === labTestData.id,
              )

              return {
                ...test,
                department: testDepartment,
                fileMetadata: testFileMetadata,
              }
            }),
          )

          const groupedTests = updatedLabTests.reduce((acc, test) => {
            if (!acc[test.department]) {
              acc[test.department] = []
            }
            acc[test.department].push(test)
            return acc
          }, {})

          const sortedDepartments = Object.keys(groupedTests).sort((a, b) => {
            if (sortField === 'department') {
              const fieldA = a.toLowerCase()
              const fieldB = b.toLowerCase()
              if (fieldA < fieldB) return sortOrder === 'asc' ? -1 : 1
              if (fieldA > fieldB) return sortOrder === 'asc' ? 1 : -1
              return 0
            }
            return 0
          })

          const sortedGroupedTests = {}
          sortedDepartments.forEach((dept) => {
            sortedGroupedTests[dept] = groupedTests[dept] // Preserve original order of lab tests
          })

          const filteredGroupedTests =
            department && department !== 'ALL'
              ? { [department]: sortedGroupedTests[department] || [] }
              : sortedGroupedTests

          const filteredTestsBySearchText = searchText
            ? Object.keys(filteredGroupedTests).reduce((acc, dept) => {
                const filteredTests = filteredGroupedTests[dept].filter(
                  (test) =>
                    test.testName
                      .toLowerCase()
                      .includes(searchText.toLowerCase()),
                )
                if (filteredTests.length > 0) {
                  acc[dept] = filteredTests
                }
                return acc
              }, {})
            : filteredGroupedTests

          const nonEmptyDepartments = Object.keys(
            filteredTestsBySearchText,
          ).reduce((acc, dept) => {
            if (filteredTestsBySearchText[dept]?.length > 0) {
              acc[dept] = filteredTestsBySearchText[dept]
            }
            return acc
          }, {})

          return {
            ...labTestData,
            labTests: nonEmptyDepartments,
          }
        }),
      )

      const filteredData = enrichedLabTestsData.filter(
        (doc) => Object.keys(doc.labTests).length > 0,
      )

      return filteredData
    } catch (error) {
      console.error('Error fetching lab tests by patient:', error)
      throw new Error('Failed to fetch lab tests')
    }
  }
  async createLabTest(labTest) {
    labTest.labTests = labTest.labTests.map((test) => ({
      ...test,
      status: LabTestStatus.UPLOAD,
    }))
    return cosmosDbContext.createItem(labTest, patientLabTestsContainer)
  }

  async updateLabTest(labTest) {
    try {
      return await cosmosDbContext.updateItem(labTest, patientLabTestsContainer)
    } catch (error) {
      console.error('Failed to update lab test:', error)
      console.error(
        'Failed lab test details:',
        JSON.stringify(labTest, null, 2),
      )
      throw error
    }
  }

  async deleteLabTest(id) {
    return cosmosDbContext.deleteItem(id, id, patientLabTestsContainer)
  }

  async getLabTestById(labTestId) {
    const query = getLabTestById(labTestId)
    return cosmosDbContext.queryItems(query, patientLabTestsContainer)
  }

  async searchPatientLabTest(
    queryString,
    pageSize = 10,
    continuationToken = null,
    patientId,
  ) {
    try {
      const query = searchTestQuery(queryString, patientId)

      const data = await cosmosDbContext.getAllItemQuery(
        patientLabTestsContainer,
        query,
        pageSize,
        continuationToken,
      )

      return data
    } catch (error) {
      console.error('Error searching patient lab tests:', error)
      return { items: [], nextToken: null }
    }
  }
}

module.exports = new LabTestRepository()
