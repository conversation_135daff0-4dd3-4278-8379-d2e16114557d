const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const paymentQueries = require('../queries/payment-query')
const { PaymentModel, PaymentStatsModel } = require('../models/payment-model')

const paymentsContainer = 'Payments'

class PaymentRepository {
  /**
   * Create a new payment record
   * @param {PaymentModel} payment - Payment model instance
   * @returns {Promise<PaymentModel>} Created payment
   */
  async createPayment(payment) {
    try {
      logging.logInfo(`Creating payment record: ${payment.id}`)
      const result = await cosmosDbContext.createItem(payment.toJSON(), paymentsContainer)
      return new PaymentModel(result)
    } catch (error) {
      logging.logError('Error creating payment:', error)
      throw error
    }
  }

  /**
   * Update payment record
   * @param {PaymentModel} payment - Payment model instance
   * @returns {Promise<PaymentModel>} Updated payment
   */
  async updatePayment(payment) {
    try {
      logging.logInfo(`Updating payment record: ${payment.id}`)
      const result = await cosmosDbContext.replaceItem(
        payment.toJSON(),
        payment.id,
        paymentsContainer
      )
      return new PaymentModel(result)
    } catch (error) {
      logging.logError('Error updating payment:', error)
      throw error
    }
  }

  /**
   * Get payment by ID
   * @param {string} paymentId - Payment ID
   * @returns {Promise<PaymentModel|null>} Payment or null if not found
   */
  async getPaymentById(paymentId) {
    try {
      logging.logInfo(`Fetching payment by ID: ${paymentId}`)
      const result = await cosmosDbContext.readItem(paymentId, paymentId, paymentsContainer)
      return result ? new PaymentModel(result) : null
    } catch (error) {
      if (error.code === 404) {
        logging.logInfo(`Payment not found: ${paymentId}`)
        return null
      }
      logging.logError('Error fetching payment by ID:', error)
      throw error
    }
  }

  /**
   * Get payment by Razorpay order ID
   * @param {string} razorpayOrderId - Razorpay order ID
   * @returns {Promise<PaymentModel|null>} Payment or null if not found
   */
  async getPaymentByOrderId(razorpayOrderId) {
    try {
      logging.logInfo(`Fetching payment by order ID: ${razorpayOrderId}`)
      const query = paymentQueries.getPaymentByOrderIdQuery(razorpayOrderId)
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      return results.length > 0 ? new PaymentModel(results[0]) : null
    } catch (error) {
      logging.logError('Error fetching payment by order ID:', error)
      throw error
    }
  }

  /**
   * Get payments by organization with pagination
   * @param {string} organizationId - Organization ID
   * @param {string} status - Payment status filter (optional)
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token
   * @returns {Promise<Object>} Paginated payments
   */
  async getPaymentsByOrganization(organizationId, status = null, pageSize = 20, continuationToken = '') {
    try {
      logging.logInfo(`Fetching payments for organization: ${organizationId}`)
      const query = paymentQueries.getPaymentsByOrganizationQuery(organizationId, status)
      const result = await cosmosDbContext.getAllItemQuery(
        paymentsContainer,
        query,
        pageSize,
        continuationToken
      )

      return {
        items: result.items ? result.items.map(item => new PaymentModel(item)) : [],
        continuationToken: result.continuationToken,
        hasMore: !!result.continuationToken,
      }
    } catch (error) {
      logging.logError('Error fetching payments by organization:', error)
      throw error
    }
  }

  /**
   * Get payments by patient ID
   * @param {string} patientId - Patient ID
   * @param {string} status - Payment status filter (optional)
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token
   * @returns {Promise<Object>} Paginated payments
   */
  async getPaymentsByPatient(patientId, status = null, pageSize = 20, continuationToken = '') {
    try {
      logging.logInfo(`Fetching payments for patient: ${patientId}`)
      const query = paymentQueries.getPaymentsByPatientQuery(patientId, status)
      const result = await cosmosDbContext.getAllItemQuery(
        paymentsContainer,
        query,
        pageSize,
        continuationToken
      )

      return {
        items: result.items ? result.items.map(item => new PaymentModel(item)) : [],
        continuationToken: result.continuationToken,
        hasMore: !!result.continuationToken,
      }
    } catch (error) {
      logging.logError('Error fetching payments by patient:', error)
      throw error
    }
  }

  /**
   * Get payments by appointment ID
   * @param {string} appointmentId - Appointment ID
   * @returns {Promise<PaymentModel[]>} List of payments
   */
  async getPaymentsByAppointment(appointmentId) {
    try {
      logging.logInfo(`Fetching payments for appointment: ${appointmentId}`)
      const query = paymentQueries.getPaymentsByAppointmentQuery(appointmentId)
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      return results.map(item => new PaymentModel(item))
    } catch (error) {
      logging.logError('Error fetching payments by appointment:', error)
      throw error
    }
  }

  /**
   * Get payments by status
   * @param {string} status - Payment status
   * @param {string} organizationId - Organization ID (optional)
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token
   * @returns {Promise<Object>} Paginated payments
   */
  async getPaymentsByStatus(status, organizationId = null, pageSize = 20, continuationToken = '') {
    try {
      logging.logInfo(`Fetching payments by status: ${status}`)
      const query = paymentQueries.getPaymentsByStatusQuery(status, organizationId)
      const result = await cosmosDbContext.getAllItemQuery(
        paymentsContainer,
        query,
        pageSize,
        continuationToken
      )

      return {
        items: result.items ? result.items.map(item => new PaymentModel(item)) : [],
        continuationToken: result.continuationToken,
        hasMore: !!result.continuationToken,
      }
    } catch (error) {
      logging.logError('Error fetching payments by status:', error)
      throw error
    }
  }

  /**
   * Get payments within date range
   * @param {string} startDate - Start date (ISO string)
   * @param {string} endDate - End date (ISO string)
   * @param {string} organizationId - Organization ID (optional)
   * @param {string} status - Payment status filter (optional)
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token
   * @returns {Promise<Object>} Paginated payments
   */
  async getPaymentsByDateRange(startDate, endDate, organizationId = null, status = null, pageSize = 20, continuationToken = '') {
    try {
      logging.logInfo(`Fetching payments by date range: ${startDate} to ${endDate}`)
      const query = paymentQueries.getPaymentsByDateRangeQuery(startDate, endDate, organizationId, status)
      const result = await cosmosDbContext.getAllItemQuery(
        paymentsContainer,
        query,
        pageSize,
        continuationToken
      )

      return {
        items: result.items ? result.items.map(item => new PaymentModel(item)) : [],
        continuationToken: result.continuationToken,
        hasMore: !!result.continuationToken,
      }
    } catch (error) {
      logging.logError('Error fetching payments by date range:', error)
      throw error
    }
  }

  /**
   * Get payment statistics for organization
   * @param {string} organizationId - Organization ID
   * @param {string} startDate - Start date (ISO string, optional)
   * @param {string} endDate - End date (ISO string, optional)
   * @returns {Promise<PaymentStatsModel>} Payment statistics
   */
  async getPaymentStats(organizationId, startDate = null, endDate = null) {
    try {
      logging.logInfo(`Fetching payment statistics for organization: ${organizationId}`)
      
      // Get all payments for the organization within date range
      const query = paymentQueries.getPaymentsByDateRangeQuery(
        startDate || new Date(0).toISOString(),
        endDate || new Date().toISOString(),
        organizationId
      )
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      const payments = results.map(item => new PaymentModel(item))

      return PaymentStatsModel.fromPayments(payments, organizationId)
    } catch (error) {
      logging.logError('Error fetching payment statistics:', error)
      throw error
    }
  }

  /**
   * Get recent payments for organization
   * @param {string} organizationId - Organization ID
   * @param {number} limit - Number of recent payments to fetch
   * @returns {Promise<PaymentModel[]>} List of recent payments
   */
  async getRecentPayments(organizationId, limit = 10) {
    try {
      logging.logInfo(`Fetching recent payments for organization: ${organizationId}`)
      const query = paymentQueries.getRecentPaymentsQuery(organizationId, limit)
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      return results.map(item => new PaymentModel(item))
    } catch (error) {
      logging.logError('Error fetching recent payments:', error)
      throw error
    }
  }

  /**
   * Get failed payments for retry
   * @param {string} organizationId - Organization ID (optional)
   * @param {number} hoursAgo - Hours ago to look for failed payments
   * @returns {Promise<PaymentModel[]>} List of failed payments
   */
  async getFailedPayments(organizationId = null, hoursAgo = 24) {
    try {
      logging.logInfo(`Fetching failed payments for retry`)
      const query = paymentQueries.getFailedPaymentsQuery(organizationId, hoursAgo)
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      return results.map(item => new PaymentModel(item))
    } catch (error) {
      logging.logError('Error fetching failed payments:', error)
      throw error
    }
  }

  /**
   * Get pending payments (created but not completed)
   * @param {string} organizationId - Organization ID (optional)
   * @param {number} hoursAgo - Hours ago to look for pending payments
   * @returns {Promise<PaymentModel[]>} List of pending payments
   */
  async getPendingPayments(organizationId = null, hoursAgo = 2) {
    try {
      logging.logInfo(`Fetching pending payments`)
      const query = paymentQueries.getPendingPaymentsQuery(organizationId, hoursAgo)
      const results = await cosmosDbContext.queryItems(query, paymentsContainer)
      return results.map(item => new PaymentModel(item))
    } catch (error) {
      logging.logError('Error fetching pending payments:', error)
      throw error
    }
  }

  /**
   * Search payments by receipt or description
   * @param {string} searchTerm - Search term
   * @param {string} organizationId - Organization ID (optional)
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token
   * @returns {Promise<Object>} Paginated search results
   */
  async searchPayments(searchTerm, organizationId = null, pageSize = 20, continuationToken = '') {
    try {
      logging.logInfo(`Searching payments with term: ${searchTerm}`)
      const query = paymentQueries.searchPaymentsQuery(searchTerm, organizationId)
      const result = await cosmosDbContext.getAllItemQuery(
        paymentsContainer,
        query,
        pageSize,
        continuationToken
      )

      return {
        items: result.items ? result.items.map(item => new PaymentModel(item)) : [],
        continuationToken: result.continuationToken,
        hasMore: !!result.continuationToken,
      }
    } catch (error) {
      logging.logError('Error searching payments:', error)
      throw error
    }
  }

  /**
   * Delete payment record
   * @param {string} paymentId - Payment ID
   * @returns {Promise<boolean>} Success status
   */
  async deletePayment(paymentId) {
    try {
      logging.logInfo(`Deleting payment record: ${paymentId}`)
      await cosmosDbContext.deleteItem(paymentId, paymentId, paymentsContainer)
      return true
    } catch (error) {
      logging.logError('Error deleting payment:', error)
      throw error
    }
  }
}

module.exports = new PaymentRepository()
