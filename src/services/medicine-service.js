const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const medicineRepository = require('../repositories/medicine-repository')
const { getMedicinesByProductIdsQuery } = require('../queries/medicine-query')

const medicineContainer = 'medicines'

class MedicineService {
  async getAllMedicines() {
    try {
      const query = getMedicinesByProductIdsQuery([])
      return await cosmosDbContext.queryItems(query, medicineContainer)
    } catch (error) {
      logging.logError('Failed to fetch medicines', error)
      return []
    }
  }

  async getMedicinesByProductIds(productIds) {
    try {
      if (!productIds.length) return []

      const query = getMedicinesByProductIdsQuery(productIds)
      const result = await cosmosDbContext.queryItems(
        query,
        medicineContainer,
        [],
      )
      return result
    } catch (error) {
      if (error.name === 'RestError' && error.code === 'PARSE_ERROR') {
        logging.logError(
          `Failed to fetch medicines by productIds due to parse error: ${error.message}`,
          error,
        )
      } else {
        logging.logError(
          `Failed to fetch medicines by productIds: ${error.message}`,
          error,
        )
      }
      return []
    }
  }

  async getMedicinesByIds(ids) {
    try {
      if (!ids.length) return []

      // Query by id field for document IDs
      const query = `SELECT * FROM c WHERE c.id IN (${ids
        .map((id) => `"${id}"`)
        .join(', ')})`

      const result = await cosmosDbContext.queryItems(
        query,
        medicineContainer,
        [],
      )

      return result
    } catch (error) {
      console.error('Failed to fetch medicines by ids', error)
      return []
    }
  }

  async bulkInsertMedicines(medicines) {
    try {
      const inserted = []
      for (const med of medicines) {
        const result = await cosmosDbContext.createItem(med, medicineContainer)
        inserted.push(result)
      }
      return inserted
    } catch (error) {
      logging.logError('Failed to bulk insert medicines', error)
      return []
    }
  }
  async searchMedicine(queryString, pageSize = 10, continuationToken = null) {
    try {
      const query = `
      SELECT * FROM c
      WHERE CONTAINS(LOWER(c.saltComposition), LOWER('${queryString}'))
         OR CONTAINS(LOWER(c.productName), LOWER('${queryString}'))
    `
      const data = await cosmosDbContext.getAllItemQuery(
        medicineContainer,
        query,
        pageSize,
        continuationToken,
      )

      return data
    } catch (error) {
      logging.logError(
        `Unable to search medicines with query: ${queryString}`,
        error,
      )
      return { items: [], nextToken: null }
    }
  }

  async searchOrganizationMedicines(
    organizationId,
    queryString,
    pageSize = 10,
    continuationToken = null,
  ) {
    try {
      // Step 1: Get all active medicine data with pricing for the organization
      const orgMedicines =
        await medicineRepository.getOrganizationMedicineIdsWithPricing(
          organizationId,
        )

      if (!orgMedicines || orgMedicines.length === 0) {
        logging.logInfo(
          `No active medicines found for organization: ${organizationId}`,
        )
        return { items: [], continuationToken: null }
      }

      // Step 2: Create a map of medicine ID to organization price
      const medicineIds = orgMedicines.map((item) => item.medicineId)
      const pricingMap = {}
      orgMedicines.forEach((item) => {
        pricingMap[item.medicineId] = item.price
      })

      // Step 3: Search in medicines container using the medicine IDs and search text
      const data = await medicineRepository.searchMedicinesByIds(
        medicineIds,
        queryString,
        pageSize,
        continuationToken,
      )

      // Step 4: Merge organization pricing with medicine data
      if (data.items && data.items.length > 0) {
        data.items = data.items.map((medicine) => ({
          ...medicine,
          organizationPrice: pricingMap[medicine.id] || 0,
        }))
      }

      return data
    } catch (error) {
      logging.logError(
        `Unable to search organization medicines for org ${organizationId} with query: ${queryString}`,
        error,
      )
      return { items: [], continuationToken: null }
    }
  }

  async fetchMedicinesForOrganization(
    organizationId,
    searchText,
    pageSize,
    page,
    continuationToken = null,
  ) {
    try {
      return await medicineRepository.fetchMedicinesForOrganization(
        organizationId,
        searchText,
        pageSize,
        page,
        continuationToken,
      )
    } catch (error) {
      console.error('Error fetching medicines for organization:', error)
      throw new Error('Failed to fetch medicines for organization')
    }
  }

  async updateOrganizationMedicines(
    organizationId,
    medicines,
    selectAll = false,
  ) {
    try {
      return await medicineRepository.updateOrganizationMedicines(
        organizationId,
        medicines,
        selectAll, // Pass the selectAll flag to the repository
      )
    } catch (error) {
      console.error('Error updating organization medicines:', error)
      throw new Error('Failed to update organization medicines')
    }
  }
}

module.exports = new MedicineService()
