const { LabTestStatus } = require('../common/constant')
const labTestRepository = require('../repositories/patient-lab-test-repository')

class LabTestService {
  async getLabTestsByPatient(
    patientId,
    dateFilter,
    sortField,
    sortOrder,
    customDateRange,
    searchText,
    department,
  ) {
    return labTestRepository.getLabTestsByPatient(
      patientId,
      dateFilter,
      sortField,
      sortOrder,
      customDateRange,
      searchText,
      department,
    )
  }

  async createLabTest(data) {
    return labTestRepository.createLabTest(data)
  }

  async updateLabTest(data) {
    return labTestRepository.updateLabTest(data)
  }

  async deleteLabTest(id) {
    return labTestRepository.deleteLabTest(id)
  }

  async getLabTestById(labTestId) {
    try {
      const result = await labTestRepository.getLabTestById(labTestId)
      return result
    } catch (error) {
      logging.logError(
        `Failed to fetch labTest ${labTestId} for patient ${patientId}`,
        error,
      )
      return []
    }
  }

  async searchPatientLabTest(
    searchText,
    pageSize,
    continuationToken,
    patientId,
  ) {
    return await labTestRepository.searchPatientLabTest(
      searchText,
      pageSize,
      continuationToken,
      patientId,
    )
  }

  async createLabTestsForPatient(patientId, labTests) {
    const labTestData = {
      patientId,
      status: LabTestStatus.UPLOAD,
      labTests: labTests.map((test) => ({
        ...test,
        status: LabTestStatus.UPLOAD,
      })),
    }
    return labTestRepository.createLabTest(labTestData)
  }
}

module.exports = new LabTestService()
