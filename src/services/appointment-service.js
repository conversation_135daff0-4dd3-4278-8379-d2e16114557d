const { parse<PERSON><PERSON><PERSON> } = require("../common/helper");
const { logError, logInfo } = require("../common/logging");
const cosmosDbContext = require("../cosmosDbContext/comosdb-context");
const appointmentContainerId = "Appointments";
const queueContainer = "Queues"

class AppointmentService {

    async deleteAppointmentById(appointmentId) {
        try {
            await cosmosDbContext.deleteItem(appointmentId, appointmentId, appointmentContainerId);
            const queues = await cosmosDbContext.queryItems(`SELECT * FROM c WHERE c.appointmentId = '${appointmentId}'`, queueContainer);
            for (const queue of queues) {
                await cosmosDbContext.deleteItem(queue.id, queue.id, queueContainer);
            }
            return { success: true };
        } catch (error) {
            logError(`Unable to delete appointment with id ${appointmentId}`, error);
            return { success: false, error: error.message };
        }
    }

    async createAppointment(appointment) {
        try {
            logInfo(`Create appointment :: ${JSON.stringify(appointment)}`)
            var result = await cosmosDbContext.createItem(appointment, appointmentContainerId)
            return result;
        } catch (error) {
            logError(`Unable to create appointment`, error)
            return null;
        }
    }

    async getAppointmentByQuery(queryString) {
        try {
            logInfo(`GET appointment with query:: ${queryString}`)
            var data = await cosmosDbContext.queryItems(queryString, appointmentContainerId);
            return data;
        } catch (error) {
            logError(`Unable to get appointment`, error)
            return null;
        }
    }

    async updateAppointment(appointment) {
        try {
            logInfo(`Update appointment :: ${JSON.stringify(appointment)}`)
            var result = await cosmosDbContext.updateItem(appointment, appointmentContainerId)
            return result;
        } catch (error) {
            logError(`Unable to update appointment`, error)
            return null;
        }
    }

    async upsertAppointment(appointmentId, appointmentData) {
        try {
            logInfo(`Upsert appointment :: ${appointmentId} :: ${JSON.stringify(appointmentData)}`)
            var result = await cosmosDbContext.patchItem(appointmentId, appointmentData, appointmentContainerId)
            return result;
        } catch (error) {
            logError(`Unable to upsert appointment`, error)
            return null;
        }
    }

    async createQueue(queue) {
        try {
            var res = await cosmosDbContext.createItem(queue, queueContainer)
            return res
        } catch (error) {
            logError(`Uable to create queue`, error);
            return null
        }
    }

    async updateQueue(queue) {
        try {
            var res = await cosmosDbContext.updateItem(queue, queueContainer);
            return res;
        } catch (error) {
            logError(`Unable to update queue`, error);
            return null
        }
    }

    async getQueueById(id) {
        try {
            var res = await cosmosDbContext.queryItems(`SELECT * FROM c WHERE c.id = '${id}'`, queueContainer);
            return res
        } catch (error) {
            logError(`Unable to get queue with id :: ${id}`, error);
            return null
        }
    }

    async getQueueByQuery(query) {
        try {
            var res = await cosmosDbContext.queryItems(query, queueContainer);
            return res;
        } catch (error) {
            logError(`Unable to get queue by query :: ${query}`, error)
            return null;
        }
    }

    async upsertQueue(queueId, queueData) {
        try {
            var res = await cosmosDbContext.patchItem(queueId, queueData, queueContainer);
            return res
        } catch (error) {
            logError(`Unable to upsert queue`, error)
            return null;
        }
    }

}

module.exports = new AppointmentService();